import { motion } from 'framer-motion';
import { LucideLoader2 } from 'lucide-react';
import Particles from '@/components/shadcnui/particles';

export default function LoadingPage() {
	return (
		<main className="relative flex min-h-screen w-full overflow-hidden bg-gradient-to-br from-background via-background/95 to-muted/30">
			{/* Background with gradients and effects - same as login page */}
			<div className="pointer-events-none absolute inset-0 z-0">
				<div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/50 to-background/20" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.15),transparent_50%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(34,197,94,0.1),transparent_50%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.08),transparent_40%)]" />
				<Particles className="absolute inset-0" quantity={100} />
			</div>

			{/* Loading content */}
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 0.8 }}
				className="relative z-10 flex w-full items-center justify-center"
			>
				<motion.div
					initial={{ opacity: 0, scale: 0.8 }}
					animate={{ opacity: 1, scale: 1 }}
					transition={{ duration: 0.6, delay: 0.2 }}
					className="flex flex-col items-center space-y-8"
				>
					{/* Logo */}
					<motion.div
						className="rounded-2xl p-4 shadow-inner"
						whileHover={{ scale: 1.05 }}
						transition={{ type: 'spring', stiffness: 400, damping: 10 }}
					>
						<img src="/assets/svgs/logo.svg" alt="StreamHub Logo" className="h-16 w-auto object-contain" />
					</motion.div>

					{/* Loading spinner */}
					<motion.div
						className="flex flex-col items-center space-y-4"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.4 }}
					>
						<div className="relative">
							<motion.div
								animate={{
									scale: [1, 1.2, 1],
									opacity: [0.3, 0.6, 0.3],
								}}
								transition={{
									duration: 2,
									repeat: Infinity,
									repeatType: 'reverse',
								}}
								className="absolute inset-0 rounded-full bg-primary/20 blur-xl"
							/>
							<LucideLoader2 
								className="relative h-12 w-12 text-primary animate-spin" 
								style={{
									filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))'
								}}
							/>
						</div>
						
						<motion.p
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.5, delay: 0.6 }}
							className="text-lg font-medium text-foreground/80"
							style={{
								textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
							}}
						>
							Carregando...
						</motion.p>
					</motion.div>
				</motion.div>
			</motion.div>

			{/* Animated decorative elements - same as login page */}
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 1.5, delay: 0.5 }}
				className="pointer-events-none absolute bottom-0 left-0 h-px w-full bg-gradient-to-r from-transparent via-primary/20 to-transparent"
			/>
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 1.5, delay: 0.7 }}
				className="pointer-events-none absolute right-0 top-0 h-full w-px bg-gradient-to-b from-transparent via-primary/10 to-transparent"
			/>
		</main>
	);
}
