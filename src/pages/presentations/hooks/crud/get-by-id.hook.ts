import { presentationsFindById } from '@/pages/presentations/api/requests/presentations/find-by-id';
import { ApiResponse } from '@/shared/types/response';
import { useQuery } from '@tanstack/react-query';
import { IPresentationInfo } from '../../../edit-apresentation/states/presentation/presentation-info.state';

export const usePresentationGetById = (id: string) => {
	return useQuery({
		queryKey: [`presentation-${id}`],
		queryFn: (): Promise<ApiResponse<IPresentationInfo>> => presentationsFindById({ id }),
		enabled: !!id,
		retry: false,
	});
};
