import useDebounce from '@/hooks/use-debounce';
import { useQuery } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import { findAllPresentations } from '../../api/requests/presentations/find-all';
import { paginationPresentationState } from '../../states/pagination.state';
import { searchPresentationState } from '../../states/search.state';

export const useFindAllPresentations = () => {
	const [search] = useAtom(searchPresentationState);
	const [pagination] = useAtom(paginationPresentationState);
	const debouncedSearch = useDebounce(search, 500);

	return useQuery({
		queryKey: ['presentations', pagination.page, pagination.pageSize, debouncedSearch],
		queryFn: () =>
			findAllPresentations({
				page: pagination.page,
				pageSize: pagination.pageSize,
				search: debouncedSearch,
			}),
	});
};
