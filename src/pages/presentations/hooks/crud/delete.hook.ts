import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { deletePresentationRequest } from '../../api/requests/presentations/delete';

export const useDeletePresentation = () => {
	const queryClient = useQueryClient();
	const deleteMutation = useMutation({
		mutationFn: async (id: string) => {
			const response = await deletePresentationRequest(id);
			if (!response.success) {
				throw new Error(response.data.message);
			}
			return response.data;
		},
		onSuccess: (data) => {
			toast.dismiss();
			toast.success(data);
			queryClient.invalidateQueries({
				queryKey: ['presentations'],
				exact: false,
			});
		},
		onError: (error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	return {
		deletePresentation: deleteMutation.mutateAsync,
	};
};
