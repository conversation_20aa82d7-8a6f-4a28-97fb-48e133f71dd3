import { IPresentationInfo } from '@/pages/edit-apresentation/states/presentation/presentation-info.state';
import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PRESENTATIONS_ROUTES } from '../../endpoints';

export const presentationsFindById = async ({ id }: { id: string }): Promise<ApiResponse<IPresentationInfo>> => {
	try {
		const response = await api.get<IPresentationInfo>(PRESENTATIONS_ROUTES.FIND_BY_ID({ id }));
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
