export const PRESENTATIONS_ROUTES = {
	CREATE: '/presentation',
	FIND_ALL: ({ page, pageSize, search }: { page: number; pageSize: number; search?: string }) => {
		const searchParam = search ? '&search=' + search : '';
		return `/presentation?page=${page}&pageSize=${pageSize}${searchParam}`;
	},
	FIND_BY_ID: ({ id }: { id: string }) => `/presentation/${id}`,
	UPDATE: ({ id }: { id: string }) => `/presentation/${id}`,
	DELETE: ({ id }: { id: string }) => `/presentation/${id}`,
} as const;
