import { Button } from '@/components/shadcnui/button';
import { Table } from '@/components/shadcnui/table';
import { useIsMobile } from '@/hooks/use-is-mobile';
import { CircularProgress, useDisclosure } from '@nextui-org/react';
import { ColumnDef } from '@tanstack/react-table';
import { LucidePresentation } from 'lucide-react';
import { usePresentationsTable } from '../../hooks/table/presentations-table.hook';
import { IPresentationData, IPresentationFindAll } from '../../api/requests/presentations/find-all';
import { CreatePresentationModal } from '../create-new-presentation/modal';
import { PresentationsTableBody } from './body';
import { PresentationCard } from './card';
import { PresentationsTableHeader } from './header';
import { PresentationsDataTablePagination } from './pagination/container';
import { PresentationDataTableToolbar } from './toolbar/toolbar';

export interface IPresentationsTable {
	data: IPresentationFindAll;
	columns: ColumnDef<IPresentationData>[];
	isLoading?: boolean;
	messageError?: string;
}

export const TablePresentations = ({ data, columns, isLoading, messageError }: IPresentationsTable) => {
	const modal = useDisclosure();
	const { table } = usePresentationsTable({ data: data.data, columns });
	const isMobile = useIsMobile(640);

	const renderContent = () => {
		if (messageError) return <div className="py-4 text-center font-medium text-red-500">{messageError}</div>;
		if (isLoading) return <CircularProgress className="mx-auto" aria-label="loading..." />;
		if (isMobile) {
			return (
				<div className="flex flex-col gap-2">
					<div className="mb-2 rounded-lg bg-primary/10 px-3 py-2 text-center text-xs text-primary">
						No mobile, só é possível adicionar e visualizar as apresentações existentes.
					</div>
					{data.data.map((presentation) => (
						<PresentationCard key={presentation.id} presentation={presentation} />
					))}
				</div>
			);
		}
		return (
			<Table>
				<PresentationsTableHeader table={table} />
				<PresentationsTableBody table={table} columns={columns} />
			</Table>
		);
	};

	return (
		<section className="relative h-full space-y-4 overflow-hidden rounded-xl px-2 py-2 transition-all duration-100 sm:space-y-6 sm:px-4 sm:py-4">
			<PresentationDataTableToolbar table={table} />
			<div className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground sm:text-sm">Gerencie todas as suas apresentações em um só lugar</p>
				</div>
				<Button
					size="lg"
					className="mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto"
					onClick={modal.onOpen}
				>
					<LucidePresentation size={22} className="transition-transform duration-300 group-hover:rotate-[15deg] group-hover:scale-110" />
					<span className="ml-2 inline">Nova Apresentação</span>
				</Button>
			</div>
			<div className={isMobile ? undefined : 'overflow-x-auto rounded-lg border-2 border-white/10'}>
				{renderContent()}
				<PresentationsDataTablePagination table={table} totalPages={data.totalPages} isMobile={isMobile} />
			</div>

			<CreatePresentationModal
				size="3xl"
				backdrop="blur"
				isOpen={modal.isOpen}
				onOpenChange={modal.onOpenChange}
				className="border border-input bg-background"
				onClose={modal.onClose}
			/>
		</section>
	);
};
