import { TableBody, TableCell, TableRow } from '@/components/shadcnui/table';
import { ColumnDef, Table, flexRender } from '@tanstack/react-table';
import { IPresentationData } from '../../../api/requests/presentations/find-all';

interface TableBodyProps {
	table: Table<IPresentationData>;
	columns: ColumnDef<IPresentationData>[];
}

export const PresentationsTableBody = ({ table, columns }: TableBodyProps) => {
	return (
		<TableBody>
			{table?.getRowModel()?.rows.length ? (
				table?.getRowModel()?.rows.map((row) => (
					<TableRow
						key={row.id}
						className={`cursor-pointer transition-colors hover:bg-[#232323]/70 hover:text-white ${
							row.getIsSelected() ? 'bg-[#232323] text-white' : ''
						}`}
					>
						{row.getVisibleCells().map((cell) => (
							<TableCell key={cell.id} className={`px-4 py-3 text-base font-medium`}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				))
			) : (
				<TableRow>
					<TableCell colSpan={columns.length} className="h-24 text-center">
						Sem resultados.
					</TableCell>
				</TableRow>
			)}
		</TableBody>
	);
};
