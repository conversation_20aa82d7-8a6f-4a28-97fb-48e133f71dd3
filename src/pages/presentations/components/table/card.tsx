import { <PERSON>, CardBody, CardHeader } from '@nextui-org/react';
import { CalendarDays, Edit3, FileText, Image as ImageIcon, Ratio } from 'lucide-react';
import { IPresentationData } from '../../api/requests/presentations/find-all';

interface PresentationCardProps {
	readonly presentation: IPresentationData;
}

export function PresentationCard({ presentation }: PresentationCardProps) {
	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return `${date.toLocaleDateString()} às ${date.toLocaleTimeString()}`;
	};

	return (
		<Card className="mb-3 w-full rounded-lg bg-muted text-white shadow-lg transition-all hover:shadow-primary/40">
			<CardHeader className="flex items-center gap-3 border-b border-white/10 pb-3">
				<ImageIcon size={24} className="text-primary" />
				<div className="flex flex-col">
					<span className="text-lg font-bold text-primary">{presentation.title}</span>
				</div>
			</CardHeader>
			<CardBody className="space-y-3 py-4">
				{presentation.description && (
					<div className="flex items-start gap-2">
						<FileText size={16} className="mt-1 text-gray-400" />
						<p className="text-sm text-gray-300">{presentation.description}</p>
					</div>
				)}
				<div className="flex items-center gap-2">
					<Ratio size={16} className="text-gray-400" />
					<p className="text-sm text-gray-300">
						Dimensões: {presentation.width}px x {presentation.height}px
					</p>
				</div>
				<div className="flex items-center gap-2">
					<CalendarDays size={16} className="text-gray-400" />
					<p className="text-sm text-gray-300">Criado em: {formatDate(presentation.createdAt)}</p>
				</div>
				<div className="flex items-center gap-2">
					<Edit3 size={16} className="text-gray-400" />
					<p className="text-sm text-gray-300">Atualizado em: {formatDate(presentation.updatedAt)}</p>
				</div>
			</CardBody>
		</Card>
	);
}
