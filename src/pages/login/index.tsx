import { Button } from '@/components/shadcnui/button';
import { Card, CardContent, CardHeader } from '@/components/shadcnui/card';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/shadcnui/tabs';
import { useLoginForm } from '@/contexts/auth/hooks/login-form.hook';
import { useLoginMutation } from '@/contexts/auth/hooks/login-mutation.hook';
import { LucideEye, LucideEyeOff, LucideLoader2, LucideShield, LucideSmartphone, LucideUser, LucideAlertCircle } from 'lucide-react';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Particles from '@/components/shadcnui/particles';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/shadcnui/tooltip';

export function Login() {
	const methods = useLoginForm();
	const { login, isLoading } = useLoginMutation();

	const [isVisible, setIsVisible] = useState(false);
	const [activeTab, setActiveTab] = useState('admin');

	const {
		formState: { errors },
	} = methods;

	function alterVisibility(event: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
		event.preventDefault();

		const input = document.querySelector('#password') as HTMLInputElement | null;

		if (input?.getAttribute('type') === 'password') {
			setIsVisible(true);
			input.setAttribute('type', 'text');
		} else {
			setIsVisible(false);
			input?.setAttribute('type', 'password');
		}
	}

	function renderAdminForm() {
		return (
			<motion.form
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				onSubmit={methods.handleSubmit((data) => {
					login(data);
				})}
				className="space-y-6"
			>
				<div className="space-y-2">
					<Label htmlFor="user" className="flex flex-row items-center gap-2 text-sm font-medium text-foreground">
						<LucideUser size={16} className="text-primary" />
						Usuário
					</Label>
					<div className="relative">
						<Input
							id="user"
							{...methods.register('user')}
							className={`h-12 border transition-all focus:bg-background focus:ring-1 focus:ring-primary/20 ${
								errors.user
									? 'border-destructive/50 bg-destructive/10 focus:border-destructive'
									: 'border-border/50 bg-background/50 focus:border-primary'
							}`}
							autoComplete="off"
							type="text"
							placeholder="Digite seu usuário"
							aria-invalid={errors.user ? 'true' : 'false'}
						/>
						{errors.user && (
							<div className="absolute right-3 top-1/2 -translate-y-1/2 text-destructive">
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<LucideAlertCircle size={16} />
										</TooltipTrigger>
										<TooltipContent className="bg-destructive text-destructive-foreground">{errors.user.message}</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</div>
						)}
					</div>
					{errors.user && (
						<motion.p
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: 'auto' }}
							exit={{ opacity: 0, height: 0 }}
							className="text-xs font-medium text-destructive"
						>
							{errors.user.message}
						</motion.p>
					)}
				</div>

				<div className="space-y-2">
					<Label htmlFor="password" className="flex flex-row items-center gap-2 text-sm font-medium text-foreground">
						<LucideShield size={16} className="text-primary" />
						Senha
					</Label>
					<div className="relative">
						<Input
							id="password"
							className={`h-12 pr-12 transition-all focus:bg-background focus:ring-1 focus:ring-primary/20 ${
								errors.password
									? 'border-destructive/50 bg-destructive/10 focus:border-destructive'
									: 'border-border/50 bg-background/50 focus:border-primary'
							}`}
							type="password"
							{...methods.register('password')}
							autoComplete="off"
							placeholder="Digite sua senha"
							maxLength={25}
							aria-invalid={errors.password ? 'true' : 'false'}
						/>
						<Button
							type="button"
							variant="ghost"
							size="icon"
							onClick={alterVisibility}
							className="absolute right-1 top-1/2 h-10 w-10 -translate-y-1/2 text-muted-foreground hover:text-foreground"
							aria-label={isVisible ? 'Ocultar senha' : 'Mostrar senha'}
						>
							<LucideEyeOff data-hide={isVisible} className="data-[hide=true]:hidden" size={16} />
							<LucideEye data-hide={!isVisible} className="data-[hide=true]:hidden" size={16} />
						</Button>
						{errors.password && (
							<div className="absolute right-12 top-1/2 -translate-y-1/2 text-destructive">
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<LucideAlertCircle size={16} />
										</TooltipTrigger>
										<TooltipContent className="bg-destructive text-destructive-foreground">
											{errors.password.message}
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</div>
						)}
					</div>
					{errors.password && (
						<motion.p
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: 'auto' }}
							exit={{ opacity: 0, height: 0 }}
							className="text-xs font-medium text-destructive"
						>
							{errors.password.message}
						</motion.p>
					)}
				</div>

				<Button
					type="submit"
					disabled={isLoading}
					style={{
						textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
					}}
					className="h-12 w-full bg-gradient-to-r from-primary to-primary/80 font-medium text-white shadow-lg transition-all hover:shadow-primary/25 disabled:opacity-70"
				>
					{isLoading ? (
						<>
							<LucideLoader2 className="mr-2 h-4 w-4 animate-spin" />
							Entrando...
						</>
					) : (
						<>
							<LucideShield className="mr-2 h-4 w-4" />
							Acessar Sistema
						</>
					)}
				</Button>
			</motion.form>
		);
	}

	function renderDeviceForm() {
		return (
			<motion.form className="space-y-6" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
				<div className="space-y-2">
					<Label htmlFor="token" className="flex flex-row items-center gap-2 text-sm font-medium text-foreground">
						<LucideSmartphone size={16} className="text-primary" />
						Token do Dispositivo
					</Label>
					<div className="relative">
						<Input
							id="token"
							type="text"
							placeholder="Digite o token do dispositivo"
							className="h-12 border-border/50 bg-background/50 transition-all focus:border-primary focus:bg-background focus:ring-1 focus:ring-primary/20"
						/>
					</div>
					<p className="text-xs text-muted-foreground">Insira o token único do dispositivo para conectá-lo</p>
				</div>
				<Button
					type="submit"
					style={{
						textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
					}}
					className="h-12 w-full bg-gradient-to-r from-primary to-primary/80 font-medium text-white shadow-lg transition-all hover:shadow-primary/25 disabled:opacity-70"
				>
					<LucideSmartphone className="mr-2 h-4 w-4" />
					Conectar Dispositivo
				</Button>
			</motion.form>
		);
	}

	return (
		<main className="relative flex min-h-screen w-full overflow-hidden bg-gradient-to-br from-background via-background/95 to-muted/30">
			<div className="pointer-events-none absolute inset-0 z-0">
				<div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/50 to-background/20" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.15),transparent_50%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(34,197,94,0.1),transparent_50%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.08),transparent_40%)]" />
				<Particles className="absolute inset-0" quantity={100} />
			</div>

			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 0.8 }}
				className="relative z-10 flex w-full items-center justify-center p-4 lg:p-8"
			>
				<div className="flex w-full max-w-[1500px] items-center justify-center gap-12 xl:justify-between">
					<motion.div
						initial={{ opacity: 0, x: -50 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.6, delay: 0.2 }}
						className="w-full max-w-md"
					>
						<Card className="border-border/30 bg-muted/60 shadow-2xl backdrop-blur-xl">
							<CardHeader className="space-y-8 pb-8 text-center">
								<div className="flex justify-center">
									<motion.div
										className="rounded-2xl p-4 shadow-inner"
										whileHover={{ scale: 1.05 }}
										transition={{ type: 'spring', stiffness: 400, damping: 10 }}
									>
										<img src="/assets/svgs/logo.svg" alt="StreamHub Logo" className="h-12 w-auto object-contain" />
									</motion.div>
								</div>
							</CardHeader>

							<CardContent className="space-y-6 pb-8">
								<Tabs defaultValue="admin" className="w-full" value={activeTab} onValueChange={setActiveTab}>
									<TabsList className="grid w-full grid-cols-2 bg-black/30 p-1">
										<TabsTrigger
											value="admin"
											className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-primary/20 data-[state=active]:text-primary"
										>
											<LucideShield size={16} />
											Administrador
										</TabsTrigger>
										<TabsTrigger
											value="device"
											className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-primary/20 data-[state=active]:text-primary"
										>
											<LucideSmartphone size={16} />
											Dispositivo
										</TabsTrigger>
									</TabsList>

									<AnimatePresence mode="wait">
										<TabsContent value="admin" className="mt-8 space-y-6">
											{renderAdminForm()}
										</TabsContent>

										<TabsContent value="device" className="mt-8 space-y-6">
											{renderDeviceForm()}
										</TabsContent>
									</AnimatePresence>
								</Tabs>
							</CardContent>
						</Card>
					</motion.div>

					{/* Enhanced illustration with animation */}
					<motion.div
						initial={{ opacity: 0, x: 50 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.6, delay: 0.4 }}
						className="hidden xl:flex xl:flex-1 xl:justify-center"
					>
						<div className="relative">
							<motion.div
								animate={{
									scale: [1, 1.05, 1],
									opacity: [0.5, 0.7, 0.5],
								}}
								transition={{
									duration: 5,
									repeat: Infinity,
									repeatType: 'reverse',
								}}
								className="absolute inset-0 rounded-full bg-primary/5 blur-3xl"
							/>
							<img
								src="/assets/svgs/illustration.svg"
								alt="StreamHub Illustration"
								className="relative h-auto max-h-[600px] w-auto object-contain drop-shadow-2xl"
							/>
						</div>
					</motion.div>
				</div>
			</motion.div>

			{/* Animated decorative elements */}
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 1.5, delay: 0.5 }}
				className="pointer-events-none absolute bottom-0 left-0 h-px w-full bg-gradient-to-r from-transparent via-primary/20 to-transparent"
			/>
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 1.5, delay: 0.7 }}
				className="pointer-events-none absolute right-0 top-0 h-full w-px bg-gradient-to-b from-transparent via-primary/10 to-transparent"
			/>
		</main>
	);
}
