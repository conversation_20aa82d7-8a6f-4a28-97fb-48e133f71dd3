import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PROGRAMMING_ENDPOINTS } from '../endpoints';

export interface IProgrammingFindByIdReturn {
	id: string;
	id_devices: string[];
	id_presentations: string[];
	createdAt: string;
	updatedAt: string;
}

export const findProgrammingById = async (id: string): Promise<ApiResponse<IProgrammingFindByIdReturn>> => {
	try {
		const response = await api.get<IProgrammingFindByIdReturn>(PROGRAMMING_ENDPOINTS.FIND_BY_ID(id));
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
