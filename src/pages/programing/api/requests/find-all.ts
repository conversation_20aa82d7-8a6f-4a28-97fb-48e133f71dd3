import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PROGRAMMING_ENDPOINTS } from '../endpoints';

export interface IPresentation {
	id: string;
	createdAt: string;
	updatedAt: string;
	title: string;
	description: string;
	width: number;
	height: number;
}

export interface IDevice {
	id: string;
	createdAt: string;
	updatedAt: string;
	name: string;
	status: boolean;
	type: string;
	width: number;
	height: number;
	resolution: string;
	brand: string;
	model: string;
	token: string;
	token_generated_at: string;
	latitude: number;
	longitude: number;
}

export interface IProgramming {
	id: string;
	createdAt: string;
	updatedAt: string;
	id_device: string;
	id_presentation: string;
	presentation: IPresentation;
	device: IDevice;
}

export interface IProgrammingFindAllResponse {
	data: IProgramming[];
	totalCount: number;
	currentPage: number;
	pageSize: number;
	totalPages: number;
}

export interface IFindAllProgrammingParams {
	page?: number;
	pageSize?: number;
	search?: string;
}

export const findAllProgramming = async (params?: IFindAllProgrammingParams): Promise<ApiResponse<IProgrammingFindAllResponse>> => {
	try {
		const searchParams = new URLSearchParams();

		if (params?.page) {
			searchParams.append('page', params.page.toString());
		}

		if (params?.pageSize) {
			searchParams.append('pageSize', params.pageSize.toString());
		}

		if (params?.search) {
			searchParams.append('search', params.search);
		}

		const url = searchParams.toString() ? `${PROGRAMMING_ENDPOINTS.FIND_ALL}?${searchParams.toString()}` : PROGRAMMING_ENDPOINTS.FIND_ALL;

		const response = await api.get<IProgrammingFindAllResponse>(url);
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
