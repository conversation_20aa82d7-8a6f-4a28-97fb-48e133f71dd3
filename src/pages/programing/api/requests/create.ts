import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { PROGRAMMING_ENDPOINTS } from '../endpoints';

export interface ICreateProgrammingDto {
	id_devices: string[];
	id_presentations: string[];
}

export interface ICreateProgrammingReturn {
	id: string;
	id_devices: string[];
	id_presentations: string[];
	createdAt: string;
	updatedAt: string;
}

export const createProgramming = async ({ items }: { items: ICreateProgrammingDto }): Promise<ApiResponse<ICreateProgrammingReturn>> => {
	try {
		const response = await api.post<ICreateProgrammingReturn>(PROGRAMMING_ENDPOINTS.CREATE, items);
		return { success: true, data: response.data, status: response.status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
