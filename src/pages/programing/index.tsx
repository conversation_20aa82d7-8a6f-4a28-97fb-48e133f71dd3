import { useState } from 'react';
import { createProgrammingColumns } from './components/table/columns/programming-columns';
import { ViewProgrammingModal } from './components/modals/view-programming-modal';
import { useDeleteProgramming } from './hooks/delete-programming.hook';
import { useFindAllProgramming } from './hooks/find-all-programming.hook';
import { IProgramming, IProgrammingFindAllResponse } from './api/requests/find-all';
import { ProgrammingDataTable } from './components/table/programming-data-table';

const initialProgrammingData: IProgrammingFindAllResponse = {
	data: [] as IProgramming[],
	currentPage: 1,
	pageSize: 10,
	totalCount: 0,
	totalPages: 1,
};

export function ProgrammingPage() {
	const { data, isLoading } = useFindAllProgramming();
	const [viewModalOpen, setViewModalOpen] = useState(false);
	const [programmingToView, setProgrammingToView] = useState<IProgramming | null>(null);
	const { deleteProgramming } = useDeleteProgramming();

	const handleDeleteProgramming = async (id: string) => {
		if (!data?.success) return;
		const programmingIds = id.split(',');

		try {
			for (const programmingId of programmingIds) {
				await deleteProgramming(programmingId.trim());
			}
		} catch (error) {
			console.error('Erro ao excluir programação:', error);
		}
	};

	const handleViewProgramming = (id: string) => {
		if (!data?.success) return;
		const currentProgramming = data.data as IProgrammingFindAllResponse;
		const programming = currentProgramming.data.find((p: IProgramming) => p.id === id);

		if (programming) {
			setProgrammingToView(programming);
			setViewModalOpen(true);
		}
	};

	const handleCloseViewModal = () => {
		setViewModalOpen(false);
		setProgrammingToView(null);
	};

	const columns = createProgrammingColumns({
		onDelete: handleDeleteProgramming,
		onView: handleViewProgramming,
	});

	return (
		<>
			<ProgrammingDataTable
				columns={columns}
				data={data?.success ? data.data : initialProgrammingData}
				isLoading={isLoading}
				messageError={!data?.success ? data?.data.message : undefined}
			/>
			<ViewProgrammingModal isOpen={viewModalOpen} onClose={handleCloseViewModal} programming={programmingToView} backdrop="blur" />
		</>
	);
}

export default ProgrammingPage;
