import { deleteProgramming } from '@/pages/programing/api/requests/delete';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export const useDeleteMultipleProgramming = (onSuccess?: () => void) => {
	const queryClient = useQueryClient();

	const { mutateAsync, isPending } = useMutation({
		mutationKey: ['delete-multiple-programming'],
		mutationFn: async (ids: string[]) => {
			const deletePromises = ids.map((id) => deleteProgramming(id));
			const results = await Promise.allSettled(deletePromises);
			const failures = results.filter((result): result is PromiseRejectedResult => result.status === 'rejected');

			if (failures.length > 0) {
				throw new Error(`Falha ao excluir ${failures.length} de ${ids.length} programações`);
			}

			return results;
		},
		onSuccess: (_, ids) => {
			const count = ids.length;
			toast.success(`${count} programaç${count > 1 ? 'ões' : 'ão'} excluída${count > 1 ? 's' : ''} com sucesso!`);
			onSuccess?.();
			queryClient.invalidateQueries({
				queryKey: ['programming'],
				exact: false,
			});
		},
		onError: (error: Error) => {
			toast.error(error.message);
		},
	});

	return {
		deleteMultipleProgramming: mutateAsync,
		isLoading: isPending,
	};
};
