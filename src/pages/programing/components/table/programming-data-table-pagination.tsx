import { Button } from '@/components/shadcnui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { LucideChevronLeft, LucideChevronRight, LucideChevronsLeft, LucideChevronsRight } from 'lucide-react';
import { IProgramming } from '@/pages/programing/api/requests/find-all';

interface IProgrammingDataTablePaginationProps {
	table: Table<IProgramming>;
	isMobile?: boolean;
}

// Animation variants
const containerVariants = {
	hidden: { opacity: 0, y: 20 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.3,
			ease: 'easeOut',
			staggerChildren: 0.1,
		},
	},
};

const itemVariants = {
	hidden: { opacity: 0, y: -2 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

const controlsVariants = {
	hidden: { opacity: 0, scale: 0.95 },
	visible: {
		opacity: 1,
		scale: 1,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

const SelectedRowsInfo = ({ table }: { table: Table<IProgramming> }) => {
	const selectedCount = table.getFilteredSelectedRowModel().rows.length;
	const totalRows = table.getRowModel().rows.length;

	return (
		<motion.div key="selected-info" variants={itemVariants} className="flex w-full flex-col items-start gap-1 sm:flex-row sm:items-center sm:gap-2">
			<span className="rounded-full bg-primary px-2.5 py-0.5 text-xs font-semibold text-primary-foreground shadow">{selectedCount}</span>
			<span className="text-xs text-gray-300 sm:text-sm">
				selecionada{selectedCount > 1 ? 's' : ''} de {totalRows} programaç{totalRows > 1 ? 'ões' : 'ão'}
			</span>
		</motion.div>
	);
};

const DefaultRowsInfo = ({ table }: { table: Table<IProgramming> }) => {
	const totalRows = table.getRowModel().rows.length;
	const filteredRows = table.getFilteredRowModel().rows.length;

	return (
		<motion.div key="default-info" variants={itemVariants} className="w-full text-xs text-gray-300 sm:text-sm">
			Mostrando <span className="font-semibold text-white drop-shadow">{totalRows}</span> de{' '}
			<span className="font-semibold text-white drop-shadow">{filteredRows}</span> programaç
			{filteredRows > 1 ? 'ões' : 'ão'}
		</motion.div>
	);
};

const PaginationInfo = ({ table }: { table: Table<IProgramming> }) => {
	const hasSelectedRows = table.getSelectedRowModel().rows.length > 0;

	return (
		<div className="flex w-full flex-col items-start gap-1 text-xs text-muted-foreground sm:flex-row sm:items-center sm:gap-2">
			{hasSelectedRows ? <SelectedRowsInfo table={table} /> : <DefaultRowsInfo table={table} />}
		</div>
	);
};

const PaginationControls = ({ table }: { table: Table<IProgramming> }) => {
	const currentPage = table.getState().pagination.pageIndex + 1;
	const totalPages = table.getPageCount();

	return (
		<motion.div variants={controlsVariants} className="flex items-center space-x-3 rounded-md bg-black/20 px-2 py-1 shadow-inner">
			<div className="flex items-center space-x-1">
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30 lg:flex"
					onClick={() => table.setPageIndex(0)}
					disabled={!table.getCanPreviousPage()}
				>
					<span className="sr-only">Ir para a primeira página</span>
					<LucideChevronsLeft className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30"
					onClick={() => table.previousPage()}
					disabled={!table.getCanPreviousPage()}
				>
					<span className="sr-only">Ir para a página anterior</span>
					<LucideChevronLeft className="h-4 w-4" />
				</Button>
				<div className="page-status flex min-w-[80px] items-center justify-center rounded-md bg-black/40 px-2 py-1 text-sm font-semibold text-white shadow-inner">
					{currentPage}/{totalPages}
				</div>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30"
					onClick={() => table.nextPage()}
					disabled={!table.getCanNextPage()}
				>
					<span className="sr-only">Ir para a próxima página</span>
					<LucideChevronRight className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30 lg:flex"
					onClick={() => table.setPageIndex(table.getPageCount() - 1)}
					disabled={!table.getCanNextPage()}
				>
					<span className="sr-only">Ir para a última página</span>
					<LucideChevronsRight className="h-4 w-4" />
				</Button>
			</div>
		</motion.div>
	);
};

const RowsPerPageSelect = ({ table }: { table: Table<IProgramming> }) => {
	const currentPageSize = table.getState().pagination.pageSize;

	return (
		<motion.div variants={itemVariants} className="flex items-center gap-2 rounded-md border border-muted-foreground/10 bg-black/30 px-2 py-1 shadow-sm">
			<Select
				value={`${currentPageSize}`}
				onValueChange={(value) => {
					const newSize = Number(value);
					table.setPageSize(newSize);
				}}
			>
				<SelectTrigger className="h-7 w-[60px] rounded border border-muted-foreground/10 bg-black/90 text-muted-foreground shadow-none hover:border-primary/30 hover:bg-black/95 focus:ring-2 focus:ring-primary/20">
					<SelectValue placeholder={currentPageSize} />
				</SelectTrigger>
				<SelectContent side="top" className="border border-muted-foreground/10 bg-black/95 text-muted-foreground shadow-lg">
					{[5, 10, 20, 30, 40, 50].map((pageSize) => (
						<SelectItem key={pageSize} value={`${pageSize}`} className="hover:bg-primary/40 hover:text-white">
							{pageSize}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</motion.div>
	);
};

export function ProgrammingDataTablePagination({ table, isMobile }: IProgrammingDataTablePaginationProps) {
	const dontShowPagination = !table.getCanPreviousPage() && !table.getCanNextPage();

	return (
		<motion.div
			variants={containerVariants}
			initial="hidden"
			animate="visible"
			className={`${isMobile && 'rounded-lg'} flex flex-col items-center gap-4 bg-muted px-2 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-2`}
		>
			<div className={`flex w-full ${isMobile ? 'flex-row' : 'flex-col'} items-center gap-3 sm:flex-row sm:items-center sm:gap-4`}>
				<PaginationInfo table={table} />
				<RowsPerPageSelect table={table} />
			</div>
			{!dontShowPagination && (
				<div className="mt-3 flex w-full justify-center sm:mt-0 sm:w-auto sm:justify-end">
					<PaginationControls table={table} />
				</div>
			)}
		</motion.div>
	);
}
