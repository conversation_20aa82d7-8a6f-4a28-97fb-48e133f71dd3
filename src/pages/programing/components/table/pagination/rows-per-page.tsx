import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shadcnui/select';
import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { IProgramming } from '../../../api/requests/find-all';

interface RowsPerPageSelectProps {
	table: Table<IProgramming>;
}

const selectVariants = {
	hidden: { opacity: 0, scale: 0.95 },
	visible: {
		opacity: 1,
		scale: 1,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

export const RowsPerPageSelect = ({ table }: RowsPerPageSelectProps) => {
	const currentPageSize = table.getState().pagination.pageSize;

	return (
		<motion.div
			variants={selectVariants}
			className="flex items-center gap-2 rounded-md border border-muted-foreground/10 bg-black/30 px-2 py-1 shadow-sm"
		>
			<p className="hidden text-muted-foreground">Linhas por página:</p>
			<Select
				value={`${currentPageSize}`}
				onValueChange={(value) => {
					const newSize = Number(value);
					table.setPageSize(newSize);
				}}
			>
				<SelectTrigger className="h-7 w-[60px] rounded border border-muted-foreground/10 bg-black/90 text-muted-foreground shadow-none hover:border-primary/30 hover:bg-black/95 focus:ring-2 focus:ring-primary/20">
					<SelectValue placeholder={currentPageSize} />
				</SelectTrigger>
				<SelectContent side="top" className="border border-muted-foreground/10 bg-black/95 text-muted-foreground shadow-lg">
					{[5, 10, 20, 30, 40, 50].map((pageSize) => (
						<SelectItem key={pageSize} value={`${pageSize}`} className="hover:bg-primary/40 hover:text-white">
							{pageSize}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</motion.div>
	);
};
