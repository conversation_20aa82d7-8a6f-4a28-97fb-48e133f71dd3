import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { IProgramming } from '../../../api/requests/find-all';

interface PaginationInfoProps {
	table: Table<IProgramming>;
}

const infoVariants = {
	hidden: { opacity: 0, y: -5 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

const SelectedRowsInfo = ({ table }: PaginationInfoProps) => {
	const selectedCount = table.getFilteredSelectedRowModel().rows.length;
	const totalRows = table.getRowModel().rows.length;

	return (
		<motion.div key="selected-info" variants={infoVariants} className="flex w-full flex-col items-start gap-1 sm:flex-row sm:items-center sm:gap-2">
			<span className="rounded-full bg-primary px-2.5 py-0.5 text-xs font-semibold text-primary-foreground shadow">{selectedCount}</span>
			<span className="text-xs text-gray-300 sm:text-sm">
				selecionada{selectedCount > 1 ? 's' : ''} de {totalRows} programaç{totalRows > 1 ? 'ões' : 'ão'}
			</span>
		</motion.div>
	);
};

const DefaultRowsInfo = ({ table }: PaginationInfoProps) => {
	const totalRows = table.getRowModel().rows.length;
	const filteredRows = table.getFilteredRowModel().rows.length;

	return (
		<motion.div key="default-info" variants={infoVariants} className="w-full text-xs text-gray-300 sm:text-sm">
			Mostrando <span className="font-semibold text-white drop-shadow">{totalRows}</span> de{' '}
			<span className="font-semibold text-white drop-shadow">{filteredRows}</span> programaç
			{filteredRows > 1 ? 'ões' : 'ão'}
		</motion.div>
	);
};

export const PaginationInfo = ({ table }: PaginationInfoProps) => {
	const hasSelectedRows = table.getSelectedRowModel().rows.length > 0;

	return (
		<div className="flex w-full flex-col items-start gap-1 text-xs text-muted-foreground sm:flex-row sm:items-center sm:gap-2">
			{hasSelectedRows ? <SelectedRowsInfo table={table} /> : <DefaultRowsInfo table={table} />}
		</div>
	);
};
