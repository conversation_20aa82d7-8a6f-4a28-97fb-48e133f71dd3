import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { IProgramming } from '../../../api/requests/find-all';
import { PaginationControls } from './controls';
import { PaginationInfo } from './info';
import { RowsPerPageSelect } from './rows-per-page';

interface IProgrammingDataTablePagination {
	table: Table<IProgramming>;
	isMobile?: boolean;
}

const containerVariants = {
	hidden: { opacity: 0, y: 20 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.3,
			ease: 'easeOut',
			staggerChildren: 0.1,
		},
	},
};

export const ProgrammingDataTablePagination = ({ table, isMobile }: IProgrammingDataTablePagination) => {
	const dontShowPagination = !table.getCanPreviousPage() && !table.getCanNextPage();

	return (
		<motion.div
			variants={containerVariants}
			initial="hidden"
			animate="visible"
			className={`${isMobile && 'rounded-lg'} flex flex-col items-center gap-4 bg-muted px-2 py-3 sm:flex-row sm:items-center sm:justify-between sm:gap-2`}
		>
			<div className={`flex w-full ${isMobile ? 'flex-row' : 'flex-col'} items-center gap-3 sm:flex-row sm:items-center sm:gap-4`}>
				<PaginationInfo table={table} />
				<RowsPerPageSelect table={table} />
			</div>
			{!dontShowPagination && (
				<div className="mt-3 flex w-full justify-center sm:mt-0 sm:w-auto sm:justify-end">
					<PaginationControls table={table} />
				</div>
			)}
		</motion.div>
	);
};
