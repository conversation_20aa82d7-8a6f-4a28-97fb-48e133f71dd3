import { Button } from '@/components/shadcnui/button';
import { Table } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { LucideChevronLeft, LucideChevronRight, LucideChevronsLeft, LucideChevronsRight } from 'lucide-react';
import { IProgramming } from '../../../api/requests/find-all';

interface PaginationControlsProps {
	table: Table<IProgramming>;
}

const controlsVariants = {
	hidden: { opacity: 0, scale: 0.95 },
	visible: {
		opacity: 1,
		scale: 1,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

export const PaginationControls = ({ table }: PaginationControlsProps) => {
	const currentPage = table.getState().pagination.pageIndex + 1;
	const totalPages = table.getPageCount();

	return (
		<motion.div variants={controlsVariants} className="flex items-center space-x-3 rounded-md bg-black/20 px-2 py-1 shadow-inner">
			<div className="flex items-center space-x-1">
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30 lg:flex"
					onClick={() => table.setPageIndex(0)}
					disabled={!table.getCanPreviousPage()}
				>
					<span className="sr-only">Ir para a primeira página</span>
					<LucideChevronsLeft className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30"
					onClick={() => table.previousPage()}
					disabled={!table.getCanPreviousPage()}
				>
					<span className="sr-only">Ir para a página anterior</span>
					<LucideChevronLeft className="h-4 w-4" />
				</Button>
				<div className="page-status flex min-w-[80px] items-center justify-center rounded-md bg-black/40 px-2 py-1 text-sm font-semibold text-white shadow-inner">
					{currentPage}/{totalPages}
				</div>
				<Button
					variant="outline"
					className="pagination-button h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30"
					onClick={() => table.nextPage()}
					disabled={!table.getCanNextPage()}
				>
					<span className="sr-only">Ir para a próxima página</span>
					<LucideChevronRight className="h-4 w-4" />
				</Button>
				<Button
					variant="outline"
					className="pagination-button hidden h-8 w-8 rounded-md border border-white/10 bg-primary/10 p-0 text-primary shadow hover:border-primary/30 hover:bg-primary/20 hover:text-primary focus:ring-2 focus:ring-primary/30 lg:flex"
					onClick={() => table.setPageIndex(table.getPageCount() - 1)}
					disabled={!table.getCanNextPage()}
				>
					<span className="sr-only">Ir para a última página</span>
					<LucideChevronsRight className="h-4 w-4" />
				</Button>
			</div>
		</motion.div>
	);
};
