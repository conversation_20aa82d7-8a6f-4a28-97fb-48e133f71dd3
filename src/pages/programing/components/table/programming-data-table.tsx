import { Button } from '@/components/shadcnui/button';
import { Badge } from '@/components/shadcnui/badge';
import { Table } from '@/components/shadcnui/table';
import { useIsMobile } from '@/hooks/use-is-mobile';
import { IProgramming, IProgrammingFindAllResponse } from '@/pages/programing/api/requests/find-all';
import { useProgrammingTable } from '@/pages/programing/hooks/table/programming-table.hook';
import { changeIconByDeviceType } from '@/pages/devices/functions/change-icon-by-device-type';
import { formatDate } from '@/shared/lib/utils/format-date';
import { CircularProgress, useDisclosure, Card, CardBody, CardHeader } from '@nextui-org/react';
import { ColumnDef } from '@tanstack/react-table';
import { LucideCalendar, LucideEye, LucideTrash2, LucideMonitor, CalendarDays } from 'lucide-react';

import { ProgrammingDataTablePagination } from './programming-data-table-pagination';
import { ProgrammingDataTableToolbar } from './programming-data-table-toolbar';
import { ProgrammingTableBody } from './programming-table-body';
import { ProgrammingTableHeader } from './programming-table-header';
import { CreateProgrammingModal } from '../modals/create-programming-modal';

interface IProgrammingDataTableProps {
	readonly columns: ColumnDef<IProgramming>[];
	readonly data: IProgrammingFindAllResponse;
	readonly isLoading?: boolean;
	readonly messageError?: string;
}

export function ProgrammingDataTable({ columns, data, isLoading, messageError }: IProgrammingDataTableProps) {
	const { table } = useProgrammingTable({ data: data.data, columns });
	const isMobile = useIsMobile(640);
	const modalCreateProgramming = useDisclosure();

	const actionsColumn = columns.find((col) => col.id === 'actions') as any;
	const actionHandlers: {
		onView?: (id: string) => void;
		onDelete?: (id: string) => void;
		onEdit?: (id: string) => void;
		onDuplicate?: (id: string) => void;
	} = {};

	if (actionsColumn?.meta?.config) {
		Object.assign(actionHandlers, actionsColumn.meta.config);
	}

	const renderContent = () => {
		if (isMobile) {
			return (
				<div className="flex flex-col gap-3">
					{messageError ? (
						<div className="py-8 text-center">
							<p className="text-sm font-medium text-red-500">{messageError}</p>
						</div>
					) : isLoading ? (
						<div className="py-8 text-center">
							<CircularProgress className="mx-auto" aria-label="loading..." />
						</div>
					) : (
						data.data.map((programming) => (
							<Card
								key={programming.id}
								className="mb-3 w-full rounded-lg bg-muted text-white shadow-lg transition-all hover:shadow-primary/40"
							>
								<CardHeader className="flex items-center justify-between border-b border-white/10 pb-3">
									<div className="flex items-center gap-3">
										<LucideMonitor size={24} className="text-primary" />
										<div className="flex flex-col">
											<span className="text-lg font-bold text-primary">Programação {programming.id}</span>
											<span className="text-xs text-gray-400">
												{programming.device.name} - {programming.presentation.title}
											</span>
										</div>
									</div>
									<div className="flex items-center gap-1">
										{actionHandlers.onView && (
											<Button
												variant="ghost"
												size="sm"
												className="h-8 w-8 p-0 text-primary hover:bg-blue-500/10 hover:text-primary/50"
												onClick={() => actionHandlers.onView?.(programming.id)}
											>
												<LucideEye className="h-4 w-4" />
											</Button>
										)}
										{actionHandlers.onDelete && (
											<Button
												variant="ghost"
												size="sm"
												className="h-8 w-8 p-0 text-red-400 hover:bg-red-500/10 hover:text-red-300"
												onClick={() => actionHandlers.onDelete?.(programming.id)}
											>
												<LucideTrash2 className="h-4 w-4" />
											</Button>
										)}
									</div>
								</CardHeader>
								<CardBody className="space-y-3 py-4">
									<div className="flex items-center gap-2">
										{changeIconByDeviceType(programming.device.type as any)}
										<p className="text-sm text-gray-300">Dispositivo: {programming.device.name}</p>
									</div>
									<div className="flex items-center gap-2">
										<Badge
											variant={programming.device.status ? 'default' : 'secondary'}
											className={`text-xs ${programming.device.status ? 'border-green-500/30 bg-green-500/20 text-green-400' : 'border-red-500/30 bg-red-500/20 text-red-400'}`}
										>
											{programming.device.status ? 'Online' : 'Offline'}
										</Badge>
									</div>
									<div className="flex items-center gap-2">
										<p className="text-sm text-gray-300">Tipo: {programming.device.type}</p>
									</div>
									<div className="flex items-center gap-2">
										<p className="text-sm text-gray-300">Resolução: {programming.device.resolution}</p>
									</div>
									<div className="flex items-center gap-2">
										<CalendarDays size={16} className="text-gray-400" />
										<p className="text-sm text-gray-300">Criado em: {formatDate(programming.createdAt)}</p>
									</div>
								</CardBody>
							</Card>
						))
					)}
				</div>
			);
		}

		return (
			<Table>
				<ProgrammingTableHeader table={table} />
				{messageError ? (
					<tbody>
						<tr>
							<td colSpan={columns.length} className="py-16 text-center">
								<p className="text-sm font-medium text-red-500">{messageError}</p>
							</td>
						</tr>
					</tbody>
				) : isLoading ? (
					<tbody>
						<tr>
							<td colSpan={columns.length} className="py-16 text-center">
								<CircularProgress className="mx-auto" aria-label="loading..." />
							</td>
						</tr>
					</tbody>
				) : (
					<ProgrammingTableBody table={table} columns={columns} />
				)}
			</Table>
		);
	};

	return (
		<section className="relative h-full space-y-4 overflow-hidden rounded-xl px-2 py-2 transition-all duration-100 sm:space-y-6 sm:px-4 sm:py-4">
			<ProgrammingDataTableToolbar table={table} />
			<div className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground sm:text-sm">Gerencie todas as suas programações em um só lugar</p>
				</div>
				<Button
					size="lg"
					className="mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto"
					onClick={modalCreateProgramming.onOpen}
				>
					<LucideCalendar size={22} className="transition-transform duration-300 group-hover:rotate-[15deg] group-hover:scale-110" />
					<span className="ml-2 inline">Nova Programação</span>
				</Button>
			</div>
			<div className={isMobile ? undefined : 'overflow-x-auto rounded-lg border-2 border-white/10'}>
				{renderContent()}
				<ProgrammingDataTablePagination table={table} isMobile={isMobile} />
			</div>

			<CreateProgrammingModal
				onClose={modalCreateProgramming.onClose}
				backdrop="blur"
				isOpen={modalCreateProgramming.isOpen}
				onOpenChange={modalCreateProgramming.onOpenChange}
			/>
		</section>
	);
}
