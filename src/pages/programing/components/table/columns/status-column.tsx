import { Badge } from '@/components/shadcnui/badge';
import { ColumnDef } from '@tanstack/react-table';

interface ICreateStatusColumnProps {
	accessorKey: string;
	header: string;
	className?: string;
}

export const createStatusColumn = <T,>({ accessorKey, header, className }: ICreateStatusColumnProps): ColumnDef<T> => ({
	accessorKey,
	header,
	cell: ({ getValue }) => {
		const status = getValue() as boolean;
		return (
			<Badge 
				variant={status ? 'default' : 'secondary'}
				className={`${status ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-red-500/20 text-red-400 border-red-500/30'} ${className || ''}`}
			>
				{status ? 'Online' : 'Offline'}
			</Badge>
		);
	},
});
