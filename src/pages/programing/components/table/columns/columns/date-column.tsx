import { DateColumnOptions } from '@/pages/programing/types/table/index';
import { ColumnDef } from '@tanstack/react-table';
import { CalendarDaysIcon, CalendarIcon } from 'lucide-react';
import { CreateHeader, IHeaderFilter } from '@/pages/presentations/components/table/columns/headers/create-header';

const dateSortingFn = (rowA: any, rowB: any, columnId: string) => {
	const a = new Date(rowA.getValue(columnId));
	const b = new Date(rowB.getValue(columnId));

	return a.getTime() - b.getTime();
};

export const createDateColumn = <T,>({ accessorKey, header, formatter, enableSorting = true, enableHiding = true }: DateColumnOptions<T>): ColumnDef<T> => {
	const dateFilters: IHeaderFilter[] = [
		{
			type: 'orderDesc',
			label: 'Mais recente',
			icon: <CalendarDaysIcon className="mr-2 h-3.5 w-3.5" />,
			onClick: () => {},
		},
		{
			type: 'orderAsc',
			label: 'Mais antigo',
			icon: <CalendarIcon className="mr-2 h-3.5 w-3.5" />,
			onClick: () => {},
		},
	];

	return {
		accessorKey,
		header: ({ column }) => (
			<CreateHeader
				active={column.getIsSorted() !== undefined}
				title={header}
				column={column}
				extraFilters={dateFilters.map((filter) => ({
					...filter,
					onClick:
						filter.type === 'orderDesc'
							? () => {
									column.toggleSorting(true);
								}
							: () => {
									column.toggleSorting(false);
								},
				}))}
			/>
		),
		cell: ({ getValue }) => {
			const value = getValue();
			if (!value) return <span className="text-gray-300">-</span>;

			try {
				const formattedDate = formatter(String(value));
				return <span className="text-gray-300">{formattedDate}</span>;
			} catch (error) {
				console.error('Erro ao formatar data:', error);
				return <span className="text-gray-300">{String(value)}</span>;
			}
		},
		enableSorting,
		enableHiding,
		sortingFn: dateSortingFn,
	};
};
