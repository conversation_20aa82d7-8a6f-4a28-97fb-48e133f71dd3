import { CountColumnOptions } from '@/pages/programing/types/table/index';
import { ColumnDef } from '@tanstack/react-table';
import { CreateHeader } from '@/pages/presentations/components/table/columns/headers/create-header';

export const createCountColumn = <T,>({
	id,
	header,
	accessor,
	singularLabel,
	pluralLabel,
	enableSorting = true,
	enableHiding = true,
}: CountColumnOptions<T>): ColumnDef<T> => ({
	id,
	accessorKey: accessor,
	header: ({ column }) => <CreateHeader title={header} column={column} />,
	cell: ({ row }) => {
		const items = row.getValue(id) as any[];
		const count = Array.isArray(items) ? items.length : 0;

		return (
			<div className="flex flex-1 items-center justify-start">
				<span className="text-sm text-gray-300">
					{count} {count !== 1 ? pluralLabel : singularLabel}
				</span>
			</div>
		);
	},
	enableSorting,
	enableHiding,
});
