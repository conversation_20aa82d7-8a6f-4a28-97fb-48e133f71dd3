import { IProgramming } from '@/pages/programing/api/requests/find-all';
import { ProgrammingActionsConfig } from '@/pages/programing/types/table/index';
import { createSelectColumn } from '@/pages/presentations/components/table/columns/columns/select-column';
import { createTextColumn } from '@/pages/presentations/components/table/columns/columns/text-column';
import { formatDate } from '@/shared/lib/utils/format-date';
import { ColumnDef } from '@tanstack/react-table';
import { createActionsColumn } from './columns/actions-column';
import { createDateColumn } from './columns/date-column';
import { createStatusColumn } from './status-column';

type Props = ProgrammingActionsConfig & {
	customColumns?: ColumnDef<IProgramming>[];
};

export const createProgrammingColumns = ({ onEdit, onDelete, onView, onDuplicate, customColumns = [] }: Props): ColumnDef<IProgramming>[] => [
	createSelectColumn<IProgramming>(),
	createTextColumn<IProgramming>({
		accessorKey: 'device.name',
		header: 'Dispositivo',
		className: 'font-medium text-white',
	}),
	createTextColumn<IProgramming>({
		accessorKey: 'presentation.title',
		header: 'Apresentação',
		className: 'font-medium text-white',
	}),
	createStatusColumn<IProgramming>({
		accessorKey: 'device.status',
		header: 'Status',
	}),
	createTextColumn<IProgramming>({
		accessorKey: 'device.type',
		header: 'Tipo',
		className: 'text-muted-foreground',
	}),
	createTextColumn<IProgramming>({
		accessorKey: 'device.resolution',
		header: 'Resolução',
		className: 'text-muted-foreground',
	}),
	createDateColumn<IProgramming>({
		accessorKey: 'createdAt',
		header: 'Criado em',
		formatter: formatDate,
	}),
	createActionsColumn<IProgramming>({
		id: 'actions',
		config: { onEdit, onDelete, onView, onDuplicate },
	}),
	...customColumns,
];
