import { TableHead, TableHeader, TableRow } from '@/components/shadcnui/table';
import { Table, flexRender } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { IProgramming } from '@/pages/programing/api/requests/find-all';

interface IProgrammingTableHeaderProps {
	table: Table<IProgramming>;
}

export const ProgrammingTableHeader = ({ table }: IProgrammingTableHeaderProps) => {
	return (
		<TableHeader className="programming-header sticky top-0 z-10 bg-[#232323] shadow-md">
			{table.getHeaderGroups().map((headerGroup) => (
				<TableRow key={headerGroup.id} className="hover:bg-transparent">
					{headerGroup.headers.map((header) => (
						<TableHead
							key={header.id}
							colSpan={header.colSpan}
							className="border-b-2 border-primary/60 bg-[#232323] px-4 py-3 text-base font-bold tracking-wide text-white shadow-inner"
						>
							{header.isPlaceholder ? null : (
								<motion.div
									initial={{ opacity: 0, y: -10 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.3, type: 'spring' }}
									className="flex items-center gap-2"
								>
									{flexRender(header.column.columnDef.header, header.getContext())}
								</motion.div>
							)}
						</TableHead>
					))}
				</TableRow>
			))}
		</TableHeader>
	);
};
