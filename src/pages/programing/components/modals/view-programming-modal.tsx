import { Badge } from '@/components/shadcnui/badge';
import { IProgramming } from '@/pages/programing/api/requests/find-all';
import { formatDate } from '@/shared/lib/utils/format-date';
import { Modal, ModalBody, ModalContent, ModalHeader, ModalProps } from '@nextui-org/react';
import { motion } from 'framer-motion';
import { Calendar, Monitor, Tv, MapPin, Wifi, WifiOff } from 'lucide-react';

interface IViewProgrammingModalProps extends Omit<ModalProps, 'children'> {
	programming: IProgramming | null;
	onClose: () => void;
}

export const ViewProgrammingModal = ({ programming, onClose, ...modalProps }: IViewProgrammingModalProps) => {
	if (!programming) return null;

	const { device, presentation } = programming;

	return (
		<Modal {...modalProps} onClose={onClose} size="5xl" className="rounded-xl border border-[#232728] bg-muted shadow-lg" scrollBehavior="inside">
			<ModalContent>
				<ModalHeader className="flex flex-col gap-2 px-4 pb-0 sm:px-6">
					<div className="flex items-center gap-2">
						<Calendar className="h-5 w-5 text-primary sm:h-6 sm:w-6" />
						<h1 className="text-base font-semibold text-white sm:text-lg">Detalhes da Programação</h1>
					</div>
					<h3 className="inline-flex items-center gap-2 text-xs font-normal text-muted-foreground sm:text-sm">
						Informações completas sobre a programação selecionada
					</h3>
				</ModalHeader>

				<ModalBody className="px-4 pb-6 pt-2 sm:px-6">
					<div className="flex flex-col gap-4 sm:gap-6">
						{/* Informações da Programação */}
						<motion.div
							className="rounded-lg border border-primary/30 bg-primary/10 p-3 shadow-lg sm:p-4"
							initial={{ opacity: 0, y: -10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3 }}
						>
							<div className="flex items-center gap-2 pb-2">
								<div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/20 text-primary sm:h-10 sm:w-10">
									<Calendar size={16} className="sm:hidden" />
									<Calendar size={20} className="hidden sm:block" />
								</div>
								<h3 className="text-base font-bold text-white sm:text-lg">Informações da Programação</h3>
							</div>
							<div className="grid grid-cols-1 gap-2 sm:grid-cols-2 sm:gap-3">
								<div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-2">
									<span className="text-xs font-medium text-gray-400 sm:text-sm">ID:</span>
									<span className="text-sm text-white sm:text-base">{programming.id}</span>
								</div>
								<div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-2">
									<span className="text-xs font-medium text-gray-400 sm:text-sm">Criado em:</span>
									<span className="text-sm text-white sm:text-base">{formatDate(programming.createdAt)}</span>
								</div>
								<div className="flex flex-col gap-1 sm:col-span-2 sm:flex-row sm:items-center sm:gap-2">
									<span className="text-xs font-medium text-gray-400 sm:text-sm">Atualizado em:</span>
									<span className="text-sm text-white sm:text-base">{formatDate(programming.updatedAt)}</span>
								</div>
							</div>
						</motion.div>

						<div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
							{/* Informações do Dispositivo */}
							<motion.div
								className="flex flex-col gap-3 rounded-lg border border-white/10 bg-black/20 p-3 shadow-md sm:p-4"
								initial={{ opacity: 0, x: -10 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.3, delay: 0.1 }}
							>
								<div className="flex flex-col gap-2 sm:flex-row sm:items-center">
									<h4 className="flex items-center gap-2 font-medium text-primary">
										<Tv className="h-4 w-4" />
										<Badge className="bg-primary/20 text-primary hover:bg-primary/30">Dispositivo</Badge>
									</h4>
									<div className="flex items-center gap-1">
										{device.status ? <Wifi className="h-4 w-4 text-green-400" /> : <WifiOff className="h-4 w-4 text-red-400" />}
										<Badge
											variant={device.status ? 'default' : 'secondary'}
											className={`text-xs ${device.status ? 'border-green-500/30 bg-green-500/20 text-green-400' : 'border-red-500/30 bg-red-500/20 text-red-400'}`}
										>
											{device.status ? 'Online' : 'Offline'}
										</Badge>
									</div>
								</div>

								<div className="space-y-2 pl-1 sm:space-y-3">
									{/* Nome */}
									<div className="flex flex-row items-center justify-between gap-x-2 sm:justify-start sm:gap-2">
										<span className="text-xs font-medium text-gray-400 sm:w-16 sm:text-sm">Nome:</span>
										<span className="break-all text-right text-sm text-white sm:text-left sm:text-base">{device.name}</span>
									</div>
									{/* Tipo */}
									<div className="flex flex-row items-center justify-between gap-x-2 sm:justify-start sm:gap-2">
										<span className="text-xs font-medium text-gray-400 sm:w-16 sm:text-sm">Tipo:</span>
										<span className="text-right text-sm text-white sm:text-left sm:text-base">{device.type}</span>
									</div>
									{/* Marca */}
									<div className="flex flex-row items-center justify-between gap-x-2 sm:justify-start sm:gap-2">
										<span className="text-xs font-medium text-gray-400 sm:w-16 sm:text-sm">Marca:</span>
										<span className="text-right text-sm text-white sm:text-left sm:text-base">{device.brand}</span>
									</div>
									{/* Modelo */}
									<div className="flex flex-row items-center justify-between gap-x-2 sm:justify-start sm:gap-2">
										<span className="text-xs font-medium text-gray-400 sm:w-16 sm:text-sm">Modelo:</span>
										<span className="text-right text-sm text-white sm:text-left sm:text-base">{device.model}</span>
									</div>
									{/* Resolução */}
									<div className="flex flex-row items-center justify-between gap-x-2 sm:justify-start sm:gap-2">
										<span className="text-xs font-medium text-gray-400 sm:w-16 sm:text-sm">Resolução:</span>
										<span className="text-right text-sm text-white sm:text-left sm:text-base">{device.resolution}</span>
									</div>
									{/* Token */}
									<div className="flex flex-row items-center justify-between gap-x-2 sm:justify-start sm:gap-2">
										<span className="text-xs font-medium text-gray-400 sm:w-16 sm:text-sm">Token:</span>
										<span className="break-all text-right font-mono text-xs text-white sm:text-left sm:text-sm">
											{device.token}
										</span>
									</div>
								</div>
							</motion.div>

							{/* Informações da Apresentação */}
							<motion.div
								className="flex flex-col gap-3 rounded-lg border border-white/10 bg-black/20 p-4 shadow-md"
								initial={{ opacity: 0, x: 10 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.3, delay: 0.2 }}
							>
								<div className="flex items-center gap-2">
									<h4 className="flex items-center gap-2 font-medium text-primary">
										<Monitor className="h-4 w-4" />
										<Badge className="bg-primary/20 text-primary hover:bg-primary/30">Apresentação</Badge>
									</h4>
								</div>

								<div className="space-y-3 pl-1">
									<div className="flex items-center gap-2">
										<span className="w-20 text-sm font-medium text-gray-400">Título:</span>
										<span className="text-base text-white">{presentation.title}</span>
									</div>
									<div className="flex items-center gap-2">
										<span className="w-20 text-sm font-medium text-gray-400">Descrição:</span>
										<span className="text-base text-white">{presentation.description}</span>
									</div>
									<div className="flex items-center gap-2">
										<span className="w-20 text-sm font-medium text-gray-400">Dimensões:</span>
										<span className="text-base text-white">
											{presentation.width} x {presentation.height}
										</span>
									</div>
									<div className="flex items-center gap-2">
										<span className="w-20 text-sm font-medium text-gray-400">Criada em:</span>
										<span className="text-base text-white">{formatDate(presentation.createdAt)}</span>
									</div>
									<div className="flex items-center gap-2">
										<span className="w-20 text-sm font-medium text-gray-400">Atualizada em:</span>
										<span className="text-base text-white">{formatDate(presentation.updatedAt)}</span>
									</div>
								</div>
							</motion.div>
						</div>

						{/* Localização do Dispositivo */}
						<motion.div
							className="flex flex-col gap-3 rounded-lg border border-white/10 bg-black/20 p-4 shadow-md"
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3, delay: 0.3 }}
						>
							<div className="flex items-center gap-2">
								<h4 className="flex items-center gap-2 font-medium text-primary">
									<MapPin className="h-4 w-4" />
									<Badge className="bg-primary/20 text-primary hover:bg-primary/30">Localização</Badge>
								</h4>
							</div>
							<div className="pl-1">
								<div className="grid gap-3 md:grid-cols-2">
									<div className="flex items-center gap-2">
										<span className="w-16 text-sm font-medium text-gray-400">Latitude:</span>
										<span className="font-mono text-base text-white">{device.latitude}</span>
									</div>
									<div className="flex items-center gap-2">
										<span className="w-16 text-sm font-medium text-gray-400">Longitude:</span>
										<span className="font-mono text-base text-white">{device.longitude}</span>
									</div>
								</div>
							</div>
						</motion.div>
					</div>
				</ModalBody>
			</ModalContent>
		</Modal>
	);
};
