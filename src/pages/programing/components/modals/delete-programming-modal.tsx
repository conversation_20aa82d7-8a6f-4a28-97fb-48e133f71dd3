import { Button } from '@/components/shadcnui/button';
import { <PERSON>dal, ModalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from '@nextui-org/react';
import { Trash2 } from 'lucide-react';

interface IDeleteProgrammingModalProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	programmingName: string;
	isLoading: boolean;
}

export const DeleteProgrammingModal = ({ isOpen, onClose, onConfirm, programmingName, isLoading }: IDeleteProgrammingModalProps) => {
	return (
		<Modal isOpen={isOpen} onOpenChange={onClose} backdrop="blur" size="md">
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader className="flex flex-col gap-1">
							<div className="flex items-center gap-2">
								<Trash2 className="h-5 w-5 text-destructive" />
								<span>Confirmar Exclusão</span>
							</div>
						</ModalHeader>
						<ModalBody>
							<p className="text-sm text-muted-foreground">
								Tem certeza que deseja excluir <strong>{programmingName}</strong>?
							</p>
							<p className="text-xs text-muted-foreground">
								Esta ação não pode ser desfeita. A programação será permanentemente removida.
							</p>
						</ModalBody>
						<ModalFooter>
							<Button variant="outline" onClick={onClose} disabled={isLoading}>
								Cancelar
							</Button>
							<Button variant="destructive" onClick={onConfirm} disabled={isLoading}>
								{isLoading ? 'Excluindo...' : 'Excluir'}
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};
