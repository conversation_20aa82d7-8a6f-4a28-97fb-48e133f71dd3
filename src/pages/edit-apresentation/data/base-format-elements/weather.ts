import { ICON_TYPES, I<PERSON>eather, WEATHER_DISPLAY_THEME } from '../../components/properties-panel/item-editors/weather/weather-editor.type';

export const DEFAULT_WEATHER_SETTINGS: IWeather = {
	labelTwo: 'Clima',
	iconType: ICON_TYPES.CLIMACONS_ANIMATED,
	forecastMode: 'Both',
	forecastDays: 3,
	themeOptions: {
		theme: WEATHER_DISPLAY_THEME.CUSTOM,
		customTheme: {
			background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
			accent: '#00d4ff',
			text: '#585555',
			highTemp: '#ff6b6b',
			lowTemp: '#003d5c',
			shadow: 'rgba(0,0,0,0.2)',
			sunAndThunder: '#ffd93d',
			moon: '#f0f0f0',
			cloud: '#e8f4fd',
			cloudFill: '#b8e6ff',
			rain: '#74b9ff',
			snow: '#ffffff',
		},
	},
};
