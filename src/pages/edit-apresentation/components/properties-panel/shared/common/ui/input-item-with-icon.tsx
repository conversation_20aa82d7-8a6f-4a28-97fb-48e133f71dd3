import { FC, InputHTMLAttributes, useCallback, useEffect, useRef, useState } from 'react';

export type ISizeUnit = 'px' | 'pol' | 'em' | 'rem' | '%' | 'pt' | 'cm' | 's' | 'ms' | 'link' | 'text';

export interface TextInputProps extends InputHTMLAttributes<HTMLInputElement> {
	icon?: React.ReactNode;
	sizeUnit?: ISizeUnit;
	isCentered?: boolean;
	debounceTime?: number;
}

const ColorInput: FC<TextInputProps> = ({ icon, value = '', onChange, className = '', debounceTime = 300, ...inputProps }) => {
	const isValidHex = (color: string): boolean => /^#([0-9A-F]{6}|[0-9A-F]{3})$/i.test(color);
	const fallbackColor = '#ffffff';
	const [localColor, setLocalColor] = useState(value || fallbackColor);
	const timeoutRef = useRef<NodeJS.Timeout>();

	useEffect(() => {
		if (value && value !== localColor) {
			setLocalColor(value);
		}
	}, [value, localColor]);

	const updateColor = useCallback(
		(newColor: string) => {
			setLocalColor(newColor);

			if (onChange) {
				if (timeoutRef.current) {
					clearTimeout(timeoutRef.current);
				}

				timeoutRef.current = setTimeout(() => {
					const syntheticEvent = {
						target: { value: newColor },
					} as React.ChangeEvent<HTMLInputElement>;
					onChange(syntheticEvent);
				}, debounceTime);
			}
		},
		[onChange, debounceTime],
	);

	useEffect(() => {
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);

	return (
		<div className={`flex h-7 w-full items-center rounded bg-[#3a3a3a] px-2 ${className}`}>
			{icon && <span className="mr-2 text-xs text-[#6c6c6c]">{icon}</span>}
			<input
				{...inputProps}
				type="text"
				value={localColor}
				onChange={(e) => {
					updateColor(e.target.value);
				}}
				className="h-full w-full bg-transparent text-xs text-[#e0e0e0] outline-none"
			/>
			<input
				type="color"
				value={isValidHex(String(localColor)) ? String(localColor) : fallbackColor}
				onChange={(e) => {
					updateColor(e.target.value);
				}}
				className="h-5 w-5 cursor-pointer appearance-none border-none p-0"
			/>
		</div>
	);
};

const StandardInput: FC<TextInputProps> = ({ icon, sizeUnit, isCentered = false, className = '', ...inputProps }) => {
	let normalizedValue = inputProps.value;
	if (inputProps.type === 'number' && (normalizedValue === 0 || normalizedValue === '0')) {
		normalizedValue = '';
	}
	normalizedValue = normalizedValue ?? '';

	return (
		<div className={`flex h-7 w-full items-center rounded bg-[#3a3a3a] ${className}`}>
			{icon && <span className="px-2 text-xs text-[#b0b0b0]">{icon}</span>}
			<input
				id={`${inputProps.name}`}
				onClick={(e) => e.stopPropagation()}
				className={`h-full w-full bg-transparent px-2 text-xs ${isCentered ? 'text-center' : 'text-left'} text-[#f0f0f0] outline-none ${
					inputProps.type === 'number'
						? '[appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none'
						: ''
				}`}
				{...inputProps}
				value={normalizedValue}
			/>
			{sizeUnit && <span className="px-2 text-xs text-[#b0b0b0]">{sizeUnit}</span>}
		</div>
	);
};

const TextInput: FC<TextInputProps> = (props) => {
	if (props.type === 'color') return <ColorInput {...props} />;
	return <StandardInput {...props} />;
};

export default TextInput;
