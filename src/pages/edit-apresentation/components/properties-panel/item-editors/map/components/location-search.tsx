import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { AnimatePresence, motion } from 'framer-motion';
import { Search } from 'lucide-react';
import { useState } from 'react';
import { IHandleLocationSearchHook } from '../map-editor.types';

interface ILocationSearchProps {
	locationSearch: IHandleLocationSearchHook;
	placeholder?: string;
	label?: string;
}

export const LocationSearch = ({ 
	locationSearch, 
	placeholder = "Ex: Avenida Brasil, São Paulo...",
	label = "Pesquisar Localização"
}: ILocationSearchProps) => {
	const [isFocused, setIsFocused] = useState(false);

	return (
		<div className="space-y-2">
			<Label className="text-[11px] text-[#6c6c6c]">{label}</Label>
			<div className="relative">
				<Input
					placeholder={placeholder}
					value={locationSearch.searchTerm}
					onChange={(e) => locationSearch.setSearchTerm(e.target.value)}
					onFocus={() => setIsFocused(true)}
					onBlur={() => setIsFocused(false)}
					onKeyDown={(e) => {
						if (e.key === 'Enter') {
							e.preventDefault();
						}
					}}
					className="h-8 pr-8 text-xs"
				/>
				<motion.div
					animate={{
						scale: locationSearch.isSearching ? 1.1 : 1,
						rotate: locationSearch.isSearching ? 360 : 0,
					}}
					transition={{
						duration: 0.5,
						repeat: locationSearch.isSearching ? Infinity : 0,
						ease: 'linear',
					}}
					className="pointer-events-none absolute inset-y-0 right-2 flex items-center"
				>
					<Search className="h-3 w-3 text-gray-400" />
				</motion.div>

				{locationSearch.isOpen && (
					<AnimatePresence>
						<motion.div
							className="absolute z-50 mt-1 w-full"
							initial={{ opacity: 0, y: -8, scale: 0.95 }}
							animate={{ opacity: 1, y: 0, scale: 1 }}
							exit={{ opacity: 0, y: -8, scale: 0.95 }}
							transition={{
								duration: 0.2,
								type: 'spring',
								stiffness: 500,
								damping: 30,
							}}
						>
							<div className="w-full overflow-hidden rounded-md border border-white/10 bg-[#2a2a2a] shadow-lg">
								{locationSearch.isSearching ? (
									<div className="flex items-center gap-2 px-3 py-2 text-xs text-gray-400">
										<div className="h-3 w-3 animate-spin rounded-full border border-gray-400 border-t-transparent"></div>
										Buscando endereços...
									</div>
								) : locationSearch.suggestions.length === 0 ? (
									<div className="px-3 py-2 text-xs text-gray-400">
										Nenhum resultado encontrado
									</div>
								) : (
									<div className="max-h-32 overflow-y-auto">
										{locationSearch.suggestions.map((suggestion, index) => (
											<button
												key={`${suggestion.position.lat}-${suggestion.position.lng}-${index}`}
												type="button"
												className="w-full px-3 py-2 text-left text-xs text-white hover:bg-[#3a3a3a] focus:bg-[#3a3a3a]"
												onClick={() => locationSearch.handleSuggestionSelect(suggestion)}
											>
												<div className="truncate">{suggestion.description}</div>
												<div className="text-[10px] text-gray-400">
													{suggestion.position.lat.toFixed(4)}, {suggestion.position.lng.toFixed(4)}
												</div>
											</button>
										))}
									</div>
								)}
							</div>
						</motion.div>
					</AnimatePresence>
				)}
			</div>
		</div>
	);
};
