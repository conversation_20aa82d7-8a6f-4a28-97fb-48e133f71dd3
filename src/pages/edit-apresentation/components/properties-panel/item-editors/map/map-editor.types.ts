import { IBorder } from '@/pages/edit-apresentation/types/elements/border-type';

export interface IMapMarker {
	id: string;
	position: {
		lat: number;
		lng: number;
	};
	title?: string;
	description?: string;
	draggable: boolean;
}

export interface IMapPreview {
	center: {
		lat: number;
		lng: number;
	};
	zoom: number;
	markers: IMapMarker[];
	gestureHandling: 'cooperative' | 'greedy' | 'none' | 'auto';
	disableDefaultUI: boolean;
	colorScheme: 'LIGHT' | 'DARK';
	opacity: number;
	border: IBorder;
	backgroundColor: string;
}

export interface IHandleMapEditorProps {
	content?: IMapPreview;
	onChange: (data: IMapPreview) => void;
	idElement: string;
}

export interface IHandleMapMarkerHook {
	markers: IMapMarker[];
	addMarker: (position: { lat: number; lng: number }) => void;
	updateMarker: (id: string, updates: Partial<IMapMarker>) => void;
	removeMarker: (id: string) => void;
	clearMarkers: () => void;
}

export interface IHandleMapCenterHook {
	center: { lat: number; lng: number };
	setCenter: (center: { lat: number; lng: number }) => void;
	zoom: number;
	setZoom: (zoom: number) => void;
}
