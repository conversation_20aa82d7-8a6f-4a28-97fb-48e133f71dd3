import { calculateMapBounds, shouldAutoAdjustMap } from '@/components/presentation-elements/elements-overlay-base/map/map-utils';
import { useCallback } from 'react';
import { IMapMarker, IMapPreview } from '../map-editor.types';

interface IUseMapBoundsProps {
	formData: IMapPreview;
	updateField: (field: keyof IMapPreview, value: any) => void;
}

export interface IHandleMapBoundsHook {
	autoAdjustMap: () => void;
	shouldShowAutoAdjust: boolean;
	canAutoAdjust: boolean;
}

export const useMapBounds = ({ formData, updateField }: IUseMapBoundsProps): IHandleMapBoundsHook => {
	const autoAdjustMap = useCallback(() => {
		const bounds = calculateMapBounds(formData.markers);
		if (bounds) {
			updateField('center', bounds.center);
			updateField('zoom', bounds.zoom);
		}
	}, [formData.markers, updateField]);

	const shouldShowAutoAdjust = shouldAutoAdjustMap(formData.markers);
	const canAutoAdjust = formData.markers.length > 0;

	return {
		autoAdjustMap,
		shouldShowAutoAdjust,
		canAutoAdjust,
	};
};
