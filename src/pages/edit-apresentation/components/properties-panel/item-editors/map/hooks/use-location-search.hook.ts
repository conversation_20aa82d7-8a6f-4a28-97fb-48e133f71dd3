import useDebounce from '@/hooks/use-debounce';
import { useEffect, useState } from 'react';
import { IHandleLocationSearchHook, ILocationSuggestion } from '../map-editor.types';

interface IUseLocationSearchProps {
	onLocationSelect: (position: { lat: number; lng: number }, description: string) => void;
}

export const useLocationSearch = ({ onLocationSelect }: IUseLocationSearchProps): IHandleLocationSearchHook => {
	const [searchTerm, setSearchTerm] = useState<string>('');
	const [suggestions, setSuggestions] = useState<ILocationSuggestion[]>([]);
	const [isSearching, setIsSearching] = useState<boolean>(false);
	const [isOpen, setIsOpen] = useState<boolean>(false);

	const debouncedSearchTerm = useDebounce(searchTerm, 500);

	useEffect(() => {
		if (debouncedSearchTerm?.trim()) {
			handleGeocodingSearch(debouncedSearchTerm);
		} else {
			setSuggestions([]);
			setIsOpen(false);
		}
	}, [debouncedSearchTerm]);

	const handleGeocodingSearch = async (input: string) => {
		if (!input.trim()) return;
		setIsSearching(true);

		try {
			if (window.google?.maps) {
				const geocoder = new window.google.maps.Geocoder();
				geocoder.geocode(
					{
						address: input,
						componentRestrictions: { country: 'br' },
					},
					(results, status) => {
						setIsSearching(false);
						if (status === window.google.maps.GeocoderStatus.OK && results && results.length > 0) {
							const formattedSuggestions = results.map((result) => ({
								description: result.formatted_address || '',
								position: {
									lat: result.geometry.location.lat(),
									lng: result.geometry.location.lng(),
								},
							}));
							setSuggestions(formattedSuggestions);
							setIsOpen(true);
						} else {
							setSuggestions([]);
							setIsOpen(false);
						}
					},
				);
			}
		} catch {
			setIsSearching(false);
			setSuggestions([]);
			setIsOpen(false);
		}
	};

	const handleSuggestionSelect = (suggestion: ILocationSuggestion) => {
		setSearchTerm('');
		onLocationSelect(suggestion.position, suggestion.description);
		setIsOpen(false);
		setSuggestions([]);
	};

	const handleSearchTermChange = (term: string) => {
		setSearchTerm(term);
		if (!term.trim()) {
			setSuggestions([]);
			setIsOpen(false);
		} else {
			setIsOpen(true);
		}
	};

	return {
		searchTerm,
		setSearchTerm: handleSearchTermChange,
		suggestions,
		isSearching,
		isOpen,
		setIsOpen,
		handleSuggestionSelect,
	};
};
