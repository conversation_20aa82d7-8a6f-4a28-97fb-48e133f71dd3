//
// Este componente permite editar mapas com marcadores personalizados.
// Os marcadores agora suportam títulos que são exibidos como overlays customizados
// usando o componente MapPopupOverlay que funciona com @vis.gl/react-google-maps
//
// Funcionalidades incluídas:
// - Marcadores com títulos personalizados
// - Overlays que aparecem baseados no nível de zoom
// - Suporte a temas claro/escuro
// - Bordas e opacidade customizáveis
//
import { Button } from '@/components/shadcnui/button';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { useFormHandler } from '@/pages/edit-apresentation/hooks/utils/form-state.hook';
import { MapPin, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import { EditorCategoryTitle } from '../../core/components/categorory-title';
import { InputGroup } from '../../core/components/input-group';
import { InputWithLabel, SelectItemProperties } from '../../shared/common';
import { BorderEditor } from '../../shared/common/property-editors/border';
import { IHandleMapEditorProps, IMapMarker, IMapPreview } from './map-editor.types';
import { DEFAULT_MAP_STYLE } from '@/pages/edit-apresentation/data/base-format-elements/map';

export const MapEditor = ({ content, onChange }: IHandleMapEditorProps) => {
	const { formData, updateField } = useFormHandler<IMapPreview>(DEFAULT_MAP_STYLE, content, onChange);
	const [newMarkerLat, setNewMarkerLat] = useState<string>('');
	const [newMarkerLng, setNewMarkerLng] = useState<string>('');
	const [newMarkerTitle, setNewMarkerTitle] = useState<string>('');

	const addMarker = () => {
		const lat = parseFloat(newMarkerLat);
		const lng = parseFloat(newMarkerLng);

		if (isNaN(lat) || isNaN(lng)) return;

		const newMarker: IMapMarker = {
			id: `marker-${Date.now()}`,
			position: { lat, lng },
			title: newMarkerTitle || 'Novo Marcador',
			description: '',
			draggable: true,
		};

		updateField('markers', [...formData.markers, newMarker]);
		setNewMarkerLat('');
		setNewMarkerLng('');
		setNewMarkerTitle('');
	};

	const removeMarker = (markerId: string) => {
		updateField(
			'markers',
			formData.markers.filter((marker) => marker.id !== markerId),
		);
	};

	return (
		<section aria-labelledby="map-editor-title" className="flex flex-col space-y-4">
			<EditorCategoryTitle title="Mapa" className="text-xs uppercase" />
			<InputGroup columns={2} ariaLabel="Centro do Mapa">
				<InputWithLabel
					label="Latitude"
					type="number"
					value={formData.center.lat}
					onChange={(e) => updateField('center', { ...formData.center, lat: parseFloat(e.target.value) || 0 })}
				/>
				<InputWithLabel
					label="Longitude"
					type="number"
					value={formData.center.lng}
					onChange={(e) => updateField('center', { ...formData.center, lng: parseFloat(e.target.value) || 0 })}
				/>
			</InputGroup>
			<InputGroup columns={2} ariaLabel="Configurações de Visualização">
				<InputWithLabel label="Zoom" type="number" value={formData.zoom} onChange={(e) => updateField('zoom', parseInt(e.target.value) || 13)} />
				<InputWithLabel
					label="Opacidade"
					type="number"
					sizeUnit="%"
					value={Math.round(formData.opacity * 100)}
					onChange={(e) => updateField('opacity', (parseInt(e.target.value) || 100) / 100)}
				/>
			</InputGroup>
			<InputGroup columns={1} ariaLabel="Aparência do Mapa">
				<div className="flex flex-col gap-2">
					<SelectItemProperties
						value={formData.colorScheme}
						onValueChange={(value) => updateField('colorScheme', value as 'LIGHT' | 'DARK')}
						items={[
							{ value: 'LIGHT', label: 'Claro' },
							{ value: 'DARK', label: 'Escuro' },
						]}
						labelText="Esquema de Cores:"
					/>
				</div>
			</InputGroup>
			<InputGroup ariaLabel="Configurações da borda">
				<BorderEditor
					border={formData.border}
					onChange={(field, value) => {
						updateField('border', { ...formData.border, [field]: value });
					}}
				/>
			</InputGroup>
			<div className="space-y-3">
				<EditorCategoryTitle title="Marcadores" className="text-xs uppercase" />
				<div className="max-h-32 space-y-2 overflow-y-auto">
					{formData.markers.map((marker) => (
						<div key={marker.id} className="flex items-center gap-2 rounded bg-[#3a3a3a] p-2 text-xs">
							<MapPin className="h-3 w-3 text-primary" />
							<span className="flex-1 truncate">{marker.title}</span>
							<span className="text-[10px] text-gray-400">
								{marker.position.lat.toFixed(4)}, {marker.position.lng.toFixed(4)}
							</span>
							<Button
								size="sm"
								variant="ghost"
								onClick={() => removeMarker(marker.id)}
								className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
							>
								<Trash2 className="h-3 w-3" />
							</Button>
						</div>
					))}
				</div>

				<div className="space-y-2 rounded bg-[#3a3a3a] p-3">
					<Label className="text-[11px] text-[#6c6c6c]">Adicionar Marcador</Label>
					<div className="grid grid-cols-2 gap-2">
						<Input placeholder="Latitude" value={newMarkerLat} onChange={(e) => setNewMarkerLat(e.target.value)} className="h-8 text-xs" />
						<Input placeholder="Longitude" value={newMarkerLng} onChange={(e) => setNewMarkerLng(e.target.value)} className="h-8 text-xs" />
					</div>
					<Input
						placeholder="Título do marcador"
						value={newMarkerTitle}
						onChange={(e) => setNewMarkerTitle(e.target.value)}
						className="h-8 text-xs"
					/>
					<Button size="sm" onClick={addMarker} disabled={!newMarkerLat || !newMarkerLng} className="h-8 w-full text-xs">
						<Plus className="mr-1 h-3 w-3" />
						Adicionar Marcador
					</Button>
				</div>
			</div>
		</section>
	);
};
