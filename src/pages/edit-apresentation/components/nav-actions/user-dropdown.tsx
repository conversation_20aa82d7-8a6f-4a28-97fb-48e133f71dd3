import { Avatar } from '@nextui-org/react';
import { HelpCircle, LogOut, Shield, User, Building, MapPin } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { useUserDropdownActions } from '../../hooks/user/use-user-dropdown-actions.hook';
import { useUserInfo } from '../../hooks/user/use-user-info.hook';
import { getDisplayName, formatRolesText } from './utils/user-display.utils';

interface IUserDropdownProps {
	className?: string;
}

export const UserDropdown: React.FC<IUserDropdownProps> = ({ className }) => {
	const { userInfo, isUserLoggedIn } = useUserInfo();
	const { handleLogout, handleHelp, isLoggingOut } = useUserDropdownActions();

	if (!isUserLoggedIn || !userInfo) {
		return null;
	}

	const displayName = getDisplayName(userInfo.name, userInfo.socialName);
	const rolesText = formatRolesText(userInfo.roles.length);

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<button
					type="button"
					className={`flex items-center gap-2 rounded-md px-1 py-1 transition hover:bg-gray-800/50 focus:outline-none ${className}`}
					aria-label="Abrir menu do usuário"
				>
					<Avatar
						size="sm"
						name={userInfo.initials}
						className="ring-2 ring-green-400 ring-offset-1 ring-offset-[#121214] transition-transform hover:scale-105"
					/>
				</button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				align="end"
				className="w-80 border border-gray-800/60 bg-gradient-to-br from-[#1c1c20] via-[#121214] to-[#1a1a1d] p-0 shadow-xl shadow-green-500/5"
			>
				<div className="border-b border-gray-800/50 bg-gradient-to-r from-green-500/10 to-transparent p-4">
					<div className="flex items-center gap-3">
						<Avatar size="lg" name={userInfo.initials} className="ring-2 ring-green-400 ring-offset-1 ring-offset-[#121214]" />
						<div className="flex-1">
							<h3 className="font-semibold text-white">{displayName}</h3>
							<p className="text-sm text-gray-400">{userInfo.email}</p>
							<div className="mt-2 space-y-1">
								{userInfo.position && (
									<div className="flex items-center gap-2">
										<User size={12} className="text-purple-400" />
										<span className="text-xs text-gray-300">{userInfo.position}</span>
									</div>
								)}
								{userInfo.department && (
									<div className="flex items-center gap-2">
										<Building size={12} className="text-orange-400" />
										<span className="text-xs text-gray-300">{userInfo.department}</span>
									</div>
								)}
								{userInfo.unit && (
									<div className="flex items-center gap-2">
										<MapPin size={12} className="text-cyan-400" />
										<span className="text-xs text-gray-300">{userInfo.unit}</span>
									</div>
								)}
							</div>
							<div className="mt-2 flex items-center gap-2">
								<div className="h-2 w-2 rounded-full bg-green-400"></div>
								<span className="text-xs text-green-400">Online</span>
								{userInfo.roles.length > 0 && (
									<>
										<span className="text-xs text-gray-500">•</span>
										<div className="flex items-center gap-1">
											<Shield size={12} className="text-blue-400" />
											<span className="text-xs text-blue-400">{rolesText}</span>
										</div>
									</>
								)}
							</div>
						</div>
					</div>
				</div>
				<div className="p-2">
					<DropdownMenuItem
						onClick={handleHelp}
						className="flex items-center gap-3 rounded-md px-4 py-3 text-white transition-colors hover:bg-purple-400/10 hover:text-purple-400 focus:bg-purple-400/10 focus:text-purple-400"
					>
						<HelpCircle size={18} className="text-purple-400" />
						<span className="font-medium">Ajuda & Suporte</span>
					</DropdownMenuItem>
					<DropdownMenuSeparator className="my-2 bg-gray-800/50" />
					<DropdownMenuItem
						onClick={handleLogout}
						disabled={isLoggingOut}
						className="flex items-center gap-3 rounded-md px-4 py-3 text-red-400 transition-colors hover:bg-red-400/10 hover:text-red-300 focus:bg-red-400/10 focus:text-red-300 disabled:opacity-50"
					>
						<LogOut size={18} />
						<span className="font-medium">{isLoggingOut ? 'Saindo...' : 'Sair'}</span>
					</DropdownMenuItem>
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
};
