'use client';

import { But<PERSON> } from '@/components/shadcnui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import { INavElement } from '@/shared/constants/my-figma';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { useAtom, useAtomValue } from 'jotai';
import { twMerge } from 'tailwind-merge';
import { nextElementAtom } from '../../../../shared/states/items/next-items.state';
import { getContextNextElement } from '../../data/base-format-elements';
import { IItemMenu } from '../../data/items/shapes-menu';
import { IItemShapeType, ITEM_SHAPE_TYPE } from '../../types/item.type';

const MENU_ITEM_CLASSES =
	'flex h-fit justify-between gap-10 rounded-none bg-[#121214] px-5 py-3 text-white focus:border-none hover:bg-[#1c1c20] hover:text-green-400 transition-colors duration-150';

interface MenuActionProps {
	menuItem: IItemMenu;
	onSelect: (shape: IItemShapeType, icon: React.ReactElement<{ className?: string }>) => void;
	isDisabled?: boolean;
	disabledReason?: string;
}

const MenuAction: React.FC<MenuActionProps> = ({ menuItem, onSelect, isDisabled = false, disabledReason }) => {
	const menuItemContent = (
		<DropdownMenuItem asChild>
			<Button
				variant="default"
				onClick={() => !isDisabled && onSelect(menuItem.value, menuItem.icon)}
				className={twMerge(MENU_ITEM_CLASSES, isDisabled && 'cursor-not-allowed opacity-50 hover:bg-[#121214] hover:text-white')}
				disabled={isDisabled}
			>
				<div className="group flex items-center gap-3">
					<span
						className={twMerge(
							'text-green-400/80 transition-colors group-hover:text-green-400',
							isDisabled && 'text-gray-500 group-hover:text-gray-500',
						)}
					>
						{menuItem.icon}
					</span>
					<p className="text-sm font-medium">{menuItem.name}</p>
				</div>
			</Button>
		</DropdownMenuItem>
	);

	if (isDisabled && disabledReason) {
		return (
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>{menuItemContent}</TooltipTrigger>
					<TooltipContent>
						<p>{disabledReason}</p>
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	}

	return menuItemContent;
};

interface DropMenuProps {
	item: INavElement;
}

export const DropMenu: React.FC<DropMenuProps> = ({ item }) => {
	const [nextElement, setNextElement] = useAtom(nextElementAtom);
	const items = useAtomValue(itemsAtom);

	const handleSelect = (shape: IItemShapeType, icon: React.ReactElement<{ className?: string }>) => {
		setNextElement({ type: shape, icon, contentDefault: getContextNextElement(shape) });
	};

	const hasWeatherItem = items.some((item) => item.type === ITEM_SHAPE_TYPE.WEATHER);

	const getMenuItemProps = (menuItem: IItemMenu) => {
		if (menuItem.value === ITEM_SHAPE_TYPE.WEATHER && hasWeatherItem) {
			return {
				isDisabled: true,
				disabledReason: 'Já existe um elemento de tempo na apresentação. Remova o existente antes de adicionar outro.',
			};
		}
		return { isDisabled: false };
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button
						variant="secondary"
						className="relative h-full w-[80px] transform rounded-md border border-gray-800/40 bg-[#121214] text-white shadow-sm transition-all duration-200 ease-in-out hover:scale-105 hover:border-green-500/30 hover:bg-[#1c1c20] hover:shadow-green-400/20 focus:ring-1 focus:ring-green-500/40"
					>
						{nextElement?.icon ?? item.icon}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent className="mt-2 flex flex-col gap-0.5 rounded-md border border-gray-800/60 bg-[#121214] p-0.5 shadow-md shadow-green-500/5">
					{Array.isArray(item.value) &&
						item.value.map((menuItem) => {
							const menuProps = getMenuItemProps(menuItem);
							return <MenuAction key={menuItem.name} menuItem={menuItem} onSelect={handleSelect} {...menuProps} />;
						})}
				</DropdownMenuContent>
			</DropdownMenu>
			<input type="file" className="hidden" accept="image/*" />
		</>
	);
};
