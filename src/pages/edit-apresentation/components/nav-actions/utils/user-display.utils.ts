import { IUserInfo, IUserDisplayConfig } from '../types/user-dropdown.types';

export const formatUserInfo = (userInfo: IUserInfo, config: IUserDisplayConfig): IUserInfo => {
	return {
		...userInfo,
		position: config.showPosition ? userInfo.position : undefined,
		department: config.showDepartment ? userInfo.department : undefined,
		unit: config.showUnit ? userInfo.unit : undefined,
		roles: config.showRoles ? userInfo.roles : [],
	};
};

export const getDisplayName = (name: string, socialName?: string | null): string => {
	return socialName || name;
};

export const formatRolesText = (rolesCount: number): string => {
	if (rolesCount === 0) return '';
	return `${rolesCount} permissão${rolesCount > 1 ? 's' : ''}`;
};
