import { isSelectionDimensionsValid } from '@/pages/edit-apresentation/hooks/canvas/interactions';
import { IShapesLayerProps } from '@/pages/edit-apresentation/types/canvas/shape';
import { nextElementAtom } from '@/shared/states/items/next-items.state';
import { useAtomValue } from 'jotai';
import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { Group, Layer, Path, Rect, Text, Transformer } from 'react-konva';
import { itemsAtom, selectedItemsIdsAtom } from '../../../../../shared/states/items/object-item.state';
import { IItem, ITEM_STATUS } from '../../../types/item.type';
import { PreviewShape } from './preview-shape';
import { renderShape } from './shape-utils';

const checkIntersection = (item1: IItem, item2: IItem) => {
	if (!item1 || !item2 || !item1.position || !item2.position || !item1.size || !item2.size) {
		return false;
	}

	const x1 = item1.position.x;
	const y1 = item1.position.y;
	const x2 = item2.position.x;
	const y2 = item2.position.y;
	const w1 = item1.size.width;
	const h1 = item1.size.height;
	const w2 = item2.size.width;
	const h2 = item2.size.height;

	return !(x1 + w1 < x2 || x2 + w2 < x1 || y1 + h1 < y2 || y2 + h2 < y1);
};

const isFullyCovered = (item: IItem, coveringItems: IItem[]) => {
	if (!item || !item.size || !item.position || !coveringItems || coveringItems.length === 0) {
		return false;
	}

	const itemArea = Math.round(item.size.width * item.size.height);
	if (itemArea <= 0) return false;

	let coveredArea = 0;

	const matrix = Array(Math.ceil(item.size.height))
		.fill(0)
		.map(() => Array(Math.ceil(item.size.width)).fill(false));

	for (const coveringItem of coveringItems) {
		if (!coveringItem || !coveringItem.size || !coveringItem.position) continue;

		const startX = Math.max(0, Math.floor(coveringItem.position.x - item.position.x));
		const startY = Math.max(0, Math.floor(coveringItem.position.y - item.position.y));
		const endX = Math.min(Math.ceil(item.size.width), Math.ceil(coveringItem.position.x + coveringItem.size.width - item.position.x));
		const endY = Math.min(Math.ceil(item.size.height), Math.ceil(coveringItem.position.y + coveringItem.size.height - item.position.y));

		for (let y = startY; y < endY; y++) {
			for (let x = startX; x < endX; x++) {
				if (y < matrix.length && x < matrix[y].length && !matrix[y][x]) {
					matrix[y][x] = true;
					coveredArea++;
				}
			}
		}
	}

	return coveredArea >= itemArea;
};

type Box = {
	x: number;
	y: number;
	width: number;
	height: number;
	rotation: number;
};

function getBoundedBoxFunc(padding: number, canvasWidth: number, canvasHeight: number) {
	return (oldBox: Box, newBox: Box): Box => {
		if (newBox.width < 5 || newBox.height < 5) return oldBox;
		const isLeftResize = newBox.x !== oldBox.x;
		const isTopResize = newBox.y !== oldBox.y;
		const minX = padding;
		const minY = padding;
		const maxX = canvasWidth - padding + 2;
		const maxY = canvasHeight - padding + 2;
		let finalX = newBox.x;
		let finalY = newBox.y;
		let finalWidth = newBox.width;
		let finalHeight = newBox.height;
		if (finalX < minX) {
			if (isLeftResize) {
				finalWidth = Math.max(5, finalWidth + finalX - minX);
				finalX = minX;
			}
		}
		if (finalY < minY) {
			if (isTopResize) {
				finalHeight = Math.max(5, finalHeight + finalY - minY);
				finalY = minY;
			}
		}
		if (finalX + finalWidth > maxX) {
			if (!isLeftResize) {
				finalWidth = maxX - finalX;
			} else {
				const rightEdge = oldBox.x + oldBox.width;
				finalWidth = rightEdge - finalX;
			}
		}
		if (finalY + finalHeight > maxY) {
			if (!isTopResize) {
				finalHeight = maxY - finalY;
			} else {
				const bottomEdge = oldBox.y + oldBox.height;
				finalHeight = bottomEdge - finalY;
			}
		}
		finalWidth = Math.max(5, finalWidth);
		finalHeight = Math.max(5, finalHeight);
		return {
			x: finalX,
			y: finalY,
			width: finalWidth,
			height: finalHeight,
			rotation: newBox.rotation,
		};
	};
}

function handleTransformerMouseDown(selectedIds: string[], onTransformStart: (e: KonvaEventObject<MouseEvent>, id: string) => void) {
	return (e: KonvaEventObject<MouseEvent>) => {
		if (selectedIds.length > 0) {
			selectedIds.forEach((id) => onTransformStart(e, id));
		}
	};
}

export function ShapesLayer(props: Readonly<IShapesLayerProps>) {
	const {
		onSelectShape,
		onDragEnd,
		onTransformEnd,
		onDragStart,
		onDragMove,
		onTransformStart,
		transformerRef,
		scale,
		hoverPosition,
		canvasWidth,
		canvasHeight,
		onShapeMouseEnter,
		onShapeMouseLeave,
		selectionBox,
		clearHoveredShape,
		handleTransform,
		currentlyHoveredItem,
		padding = 0,
	} = props;
	const items = useAtomValue(itemsAtom);

	const selectedIds = useAtomValue(selectedItemsIdsAtom);
	const nextElement = useAtomValue(nextElementAtom);
	const sortedItems = useMemo(() => [...items].filter((item) => item.status !== ITEM_STATUS.DELETED).sort((a, b) => a.layer - b.layer), [items]);

	const handleShapeMouseMove = (item: IItem) => (e: Konva.KonvaEventObject<MouseEvent>) => {
		const stage = e.target.getStage();
		const pos = stage?.getPointerPosition();
		if (pos) onShapeMouseEnter({ name: item.name, width: item.size.width, height: item.size.height, tempId: item.tempId }, pos);
	};

	const isOverlapped = useCallback(
		(item: IItem) => {
			if (selectedIds.length > 1) return false;

			if (!selectedIds.includes(item.tempId)) return false;

			const itemIndex = sortedItems.findIndex((i) => i.tempId === item.tempId);
			if (itemIndex === -1) return false;

			const coveringItems = sortedItems.slice(itemIndex + 1).filter((upperItem) => checkIntersection(item, upperItem));
			if (coveringItems.length === 0) return false;

			return isFullyCovered(item, coveringItems);
		},
		[selectedIds, sortedItems],
	);

	const hasOverlappedItems = useMemo(() => {
		return sortedItems.some((item) => isOverlapped(item));
	}, [sortedItems, isOverlapped]);

	const [warningOpacity, setWarningOpacity] = useState(0);

	useEffect(() => {
		if (hasOverlappedItems) {
			setWarningOpacity(0);
			const interval = setInterval(() => {
				setWarningOpacity((prev) => Math.min(1, prev + 0.1));
			}, 50);
			return () => clearInterval(interval);
		} else {
			setWarningOpacity(0);
		}
	}, [hasOverlappedItems]);

	return (
		<>
			<Layer>
				{/* ------------- INDICADOR DA ÁREA EDITÁVEL ----------------- */}
				<Rect
					id="editable-area-indicator"
					x={padding}
					y={padding}
					width={canvasWidth - padding * 2}
					height={canvasHeight - padding * 2}
					stroke="#666"
					strokeWidth={1}
					dash={[5, 5]}
					fill="transparent"
					listening={false}
				/>
				{/* ------------- GRUPO DO ITENS, com o PADDING a área editável ----------------- */}
				<Group x={padding} y={padding}>
					{/* ------------- SELEÇÃO DE ÁREA ----------------- */}
					{selectionBox && isSelectionDimensionsValid(selectionBox) && (
						<Rect x={selectionBox.x} y={selectionBox.y} width={selectionBox.width} height={selectionBox.height} fill="green" opacity={0.3} />
					)}

					{/* ------------- ITENS DO CANVAS ----------------- */}
					{sortedItems.map((item) => {
						const isSelected = selectedIds.includes(item.tempId);
						const isOverlapping = isOverlapped(item);
						const clickHandler = nextElement ? undefined : onSelectShape;
						return (
							<Fragment key={item.tempId}>
								{renderShape(item, {
									isSelected,
									onClick: clickHandler
										? (e: Konva.KonvaEventObject<MouseEvent>) => clickHandler({ idElement: item.tempId, event: e })
										: () => {},
									onDragEnd: (e) => {
										onDragEnd({ id: item.tempId, event: e });
										clearHoveredShape();
									},
									onTransformEnd,
									onDragStart: () => {
										onDragStart({ id: item.tempId });
										clearHoveredShape();
									},
									scale,
									canvasWidth,
									canvasHeight,
									onMouseMove: handleShapeMouseMove(item),
									onMouseLeave: onShapeMouseLeave,
									onDragMove,
									currentlyHoveredItem,
									showOnlyOutline: isOverlapping,
									stroke: isOverlapping ? 'green' : undefined,
									opacity: isOverlapping ? 0.7 : undefined,
									strokeWidth: isOverlapping ? 2 : undefined,
									fill: isOverlapping ? 'transparent' : undefined,
									padding,
								})}
							</Fragment>
						);
					})}
					{nextElement && hoverPosition && <PreviewShape shapeType={nextElement.type} x={hoverPosition.x} y={hoverPosition.y} size={80} />}

					{/* ------------- TRANSFORMER, utilitário para redimensionar o elemento ----------------- */}
					{!hasOverlappedItems && (
						<Transformer
							ref={transformerRef}
							rotateEnabled={false}
							enabledAnchors={[
								'top-left',
								'top-center',
								'top-right',
								'middle-left',
								'middle-right',
								'bottom-left',
								'bottom-center',
								'bottom-right',
							]}
							anchorSize={7}
							anchorCornerRadius={2}
							onTransform={handleTransform}
							borderDash={[4, 4]}
							anchorFill="green"
							anchorStroke="darkgreen"
							borderStroke="green"
							boundBoxFunc={getBoundedBoxFunc(padding, canvasWidth, canvasHeight)}
							onMouseDown={handleTransformerMouseDown(selectedIds, onTransformStart)}
						/>
					)}
				</Group>
			</Layer>
			{hasOverlappedItems && (
				<Layer>
					<Group x={padding + 20} y={canvasHeight - 120}>
						<Rect
							width={450}
							height={70}
							fill="rgba(0, 0, 0, 0.8)"
							opacity={warningOpacity}
							listening={false}
							cornerRadius={10}
							shadowColor="black"
							shadowBlur={10}
							shadowOpacity={0.3}
							shadowOffset={{ x: 0, y: 2 }}
						/>
						<Text
							x={50}
							y={13}
							text="O item selecionado está sobreposto por outro elemento. Use as camadas à esquerda para movê-lo para cima."
							fill="white"
							fontSize={16}
							fontStyle="bold"
							align="left"
							verticalAlign="middle"
							width={390}
							listening={false}
							opacity={warningOpacity}
						/>
						<Path x={20} y={20} data="M10,0 L0,10 L10,20" stroke="white" strokeWidth={2} fill="white" opacity={warningOpacity} />
					</Group>
				</Layer>
			)}
		</>
	);
}
