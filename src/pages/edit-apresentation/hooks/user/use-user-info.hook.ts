import {
	getUserInitials,
	getUserRoles,
	getUserDepartment,
	getUserPosition,
	getUserUnit,
	getUserSocialName,
	userState,
} from '@/contexts/auth/states/user.state';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { IUserDisplayConfig, IUserInfo } from '../../components/nav-actions/types/user-dropdown.types';
import { formatUserInfo } from '../../components/nav-actions/utils/user-display.utils';

export interface IUseUserInfo {
	userInfo: IUserInfo | null;
	isUserLoggedIn: boolean;
}

const DEFAULT_CONFIG: IUserDisplayConfig = {
	showPosition: true,
	showDepartment: true,
	showUnit: true,
	showRoles: true,
	showOnlineStatus: true,
};

export const useUserInfo = (config: IUserDisplayConfig = DEFAULT_CONFIG): IUseUserInfo => {
	const user = useAtomValue(userState);
	const userInitials = useAtomValue(getUserInitials);
	const userRoles = useAtomValue(getUserRoles);
	const userDepartment = useAtomValue(getUserDepartment);
	const userPosition = useAtomValue(getUserPosition);
	const userUnit = useAtomValue(getUserUnit);
	const userSocialName = useAtomValue(getUserSocialName);

	const userInfo = useMemo((): IUserInfo | null => {
		if (!user) return null;

		const rawUserInfo: IUserInfo = {
			name: user.name,
			socialName: userSocialName,
			email: user.email,
			initials: userInitials,
			position: userPosition,
			department: userDepartment,
			unit: userUnit,
			roles: userRoles,
		};

		return formatUserInfo(rawUserInfo, config);
	}, [user, userSocialName, userInitials, userPosition, userDepartment, userUnit, userRoles, config]);

	return {
		userInfo,
		isUserLoggedIn: Boolean(user),
	};
};
