import { useLogoutMutation } from '@/contexts/auth/hooks/logout-mutation.hook';
import { useCallback } from 'react';

export interface IUseUserDropdownActions {
	handleLogout: () => void;
	handleHelp: () => void;
	isLoggingOut: boolean;
}

export const useUserDropdownActions = (): IUseUserDropdownActions => {
	const { logout, isLoading: isLoggingOut } = useLogoutMutation();

	const handleLogout = useCallback(() => {
		logout();
	}, [logout]);

	const handleHelp = useCallback(() => {
		// Implementar lógica de ajuda no futuro
		console.log('Abrir ajuda & suporte');
	}, []);

	return {
		handleLogout,
		handleHelp,
		isLoggingOut,
	};
};
