import { IItem } from '@/pages/edit-apresentation/types/item.type';
import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useSetAtom } from 'jotai';
import Konva from 'konva';
import { useCallback, useEffect, useRef, useState } from 'react';

export interface SelectionBox {
	x: number;
	y: number;
	width: number;
	height: number;
}

interface Point {
	x: number;
	y: number;
}

const MIN_SELECTION_BOX_SIZE = 5;
const MIN_DRAG_DISTANCE = 5;

const areBoxesIntersecting = (box1: SelectionBox, box2: SelectionBox): boolean => {
	const box1Right = box1.x + box1.width;
	const box1Bottom = box1.y + box1.height;
	const box2Right = box2.x + box2.width;
	const box2Bottom = box2.y + box2.height;
	return !(box1Right <= box2.x || box1.x >= box2Right || box1Bottom <= box2.y || box1.y >= box2Bottom);
};

export const isSelectionDimensionsValid = (box: SelectionBox): boolean => box.width >= MIN_SELECTION_BOX_SIZE && box.height >= MIN_SELECTION_BOX_SIZE;

const calculateBoundedSelectionBox = (startPoint: Point, currentPoint: Point, canvasWidth: number, canvasHeight: number): SelectionBox => {
	const x = Math.min(startPoint.x, currentPoint.x);
	const y = Math.min(startPoint.y, currentPoint.y);
	const width = Math.abs(currentPoint.x - startPoint.x);
	const height = Math.abs(currentPoint.y - startPoint.y);

	const boundedX = Math.max(0, Math.min(x, canvasWidth));
	const boundedY = Math.max(0, Math.min(y, canvasHeight));
	const boundedWidth = Math.min(width, canvasWidth - boundedX);
	const boundedHeight = Math.min(height, canvasHeight - boundedY);

	return { x: boundedX, y: boundedY, width: boundedWidth, height: boundedHeight };
};

const getSelectedItemIds = (items: IItem[], selectionBox: SelectionBox, scale: number): string[] => {
	console.log('=== DEBUG SELECTION ===');
	console.log('SelectionBox:', selectionBox);
	console.log('Scale:', scale);

	return items
		.filter((item) => {
			const scaledItemBox = {
				x: item.position.x * scale,
				y: item.position.y * scale,
				width: item.size.width * scale,
				height: item.size.height * scale,
			};
			const intersects = areBoxesIntersecting(selectionBox, scaledItemBox);

			return intersects;
		})
		.map((item) => item.tempId)
		.filter((id): id is string => id !== null && id !== undefined);
};

export const useSelectionBox = (items: IItem[], isDraggingItem: boolean, canvasWidth: number, canvasHeight: number, padding: number = 0, scale: number = 1) => {
	const [selectionBox, setSelectionBox] = useState<SelectionBox | null>(null);
	const setSelectedIds = useSetAtom(selectedItemsIdsAtom);
	const initialPoint = useRef<Point | null>(null);
	const isDragging = useRef<boolean>(false);

	const handleMouseDown = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			if (e.evt.button !== 0 || isDraggingItem) return;

			const pointer = e.target.getStage()?.getPointerPosition();
			if (pointer) {
				initialPoint.current = { x: pointer.x, y: pointer.y };
				isDragging.current = false;
			}
		},
		[isDraggingItem],
	);

	const handleMouseMoveSelectionBox = useCallback(
		(e: Konva.KonvaEventObject<MouseEvent>) => {
			if (isDraggingItem || !initialPoint.current) return;

			const pointer = e.target.getStage()?.getPointerPosition();
			if (!pointer) return;

			const dx = Math.abs(pointer.x - initialPoint.current.x);
			const dy = Math.abs(pointer.y - initialPoint.current.y);

			if (dx > MIN_DRAG_DISTANCE || dy > MIN_DRAG_DISTANCE) {
				isDragging.current = true;

				const adjustedInitialPoint = {
					x: initialPoint.current.x - padding,
					y: initialPoint.current.y - padding,
				};

				const adjustedPointer = {
					x: pointer.x - padding,
					y: pointer.y - padding,
				};

				setSelectionBox(calculateBoundedSelectionBox(adjustedInitialPoint, adjustedPointer, canvasWidth, canvasHeight));
			}
		},
		[isDraggingItem, canvasWidth, canvasHeight, padding],
	);

	const handleMouseUp = useCallback(() => {
		if (!initialPoint.current) return;

		if (selectionBox && isSelectionDimensionsValid(selectionBox) && isDragging.current) {
			const newSelectedIds = getSelectedItemIds(items, selectionBox, scale);
			setSelectedIds(newSelectedIds);
		}

		setSelectionBox(null);
		initialPoint.current = null;
		isDragging.current = false;
	}, [items, selectionBox, setSelectedIds, scale]);

	useEffect(() => {
		window.addEventListener('mouseup', handleMouseUp);
		return () => window.removeEventListener('mouseup', handleMouseUp);
	}, [handleMouseUp]);

	useEffect(() => {
		if (isDraggingItem) {
			setSelectionBox(null);
			initialPoint.current = null;
			isDragging.current = false;
		}
	}, [isDraggingItem]);

	return {
		selectionBox,
		handleMouseDown,
		handleMouseMoveSelectionBox,
		handleMouseUp,
		isDragging: isDragging.current,
	};
};
