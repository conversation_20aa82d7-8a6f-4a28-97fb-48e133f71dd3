import { IMediaElement } from '../states/presentation/presentation-info.state';

export interface IPosition {
	x: number;
	y: number;
}

export interface ISize {
	width: number;
	height: number;
}

export const ITEM_SHAPE_TYPE = {
	RECTANGLE: 'rectangle',
	CLOCK: 'clock',
	CALENDAR: 'calendar',
	CAROUSEL: 'carousel',
	IMAGE: 'image',
	TEXT: 'text',
	WEATHER: 'weather',
	MAP: 'map',
} as const;

export type IItemShapeType = (typeof ITEM_SHAPE_TYPE)[keyof typeof ITEM_SHAPE_TYPE];

export const ITEM_STATUS = { NEW: 'NEW', UPDATING: 'UPDATING', SAVED: 'SAVED', DELETED: 'DELETED' } as const;
export type ITEM_STATUS = (typeof ITEM_STATUS)[keyof typeof ITEM_STATUS];

export interface IItem {
	tempId: string;
	id?: string;
	status: ITEM_STATUS;
	name: string;
	type: IItemShapeType;
	layer: number;
	position: IPosition;
	size: ISize;
	content?: object;
	isDragging?: boolean;
	media?: IMediaElement[];
}

export interface IStyleOptions {
	backgroundColor?: string;
	borderRadius?: number;
	boxShadow?: string;
	border?: string;
	opacity?: number;
}
