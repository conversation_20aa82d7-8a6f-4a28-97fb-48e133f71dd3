import { userState } from '@/contexts/auth/states/user.state';
import { StreamHubStore } from '@/main';

import { AxiosError } from 'axios';

import api from '../api/api';
import { buildUserObject } from '../api/build-user-obj';
import { AuthEventEmitter } from '../events/auth-events';
import { AUTH_CONFIG } from './auth-config';
import { getAuthCookie, removeAuthCookie, setAuthCookie } from './auth-cookies';
import { RefreshTokenManager } from './refresh-token-manager';
import { logoutRequest } from '@/contexts/auth/api/requests/logout';
import { refreshRequest } from '@/contexts/auth/api/requests/request';

const sleep = (ms: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, ms));

const handleLogout = async (token: string): Promise<void> => {
	try {
		await logoutRequest({ token });
	} catch (error) {
		console.error('Erro ao realizar logout:', error);
	} finally {
		removeAuthCookie();
		StreamHubStore.set(userState, null);
		RefreshTokenManager.resetAttempts();
		AuthEventEmitter.emit('auth:logout', {});
	}
};

export const handleAuthError = async (error: AxiosError): Promise<any> => {
	const status = error?.response?.status;

	if (status === 401 || status === 403) {
		AuthEventEmitter.emit('auth:error', { status, error });
		if (RefreshTokenManager.getAttempts() < AUTH_CONFIG.MAX_REFRESH_RETRIES) {
			const attempts = RefreshTokenManager.incrementAttempts();
			const token = getAuthCookie();
			if (!token) {
				const tokenError = new Error('Token não encontrado');
				AuthEventEmitter.emit('auth:token-expired', { error: tokenError });
				StreamHubStore.set(userState, null);
				return Promise.reject(tokenError);
			}

			try {
				await sleep(RefreshTokenManager.getBackoffDelay());
				const { data, success } = await refreshRequest({ token });

				if (success && data) {
					setAuthCookie(data);
					// Atualizar o estado do usuário com o novo token
					const updatedUser = buildUserObject(data);
					StreamHubStore.set(userState, updatedUser);
					RefreshTokenManager.resetAttempts();

					if (error.config) {
						error.config.headers.Authorization = `Bearer ${data}`;
						return api.request(error.config);
					}
					return Promise.reject(new Error('Configuração da requisição inválida'));
				}

				AuthEventEmitter.emit('auth:refresh-failed', { attempts });
				console.error('Falha ao realizar refresh token');
				await handleLogout(token);
			} catch (refreshError) {
				AuthEventEmitter.emit('auth:refresh-failed', {
					error: refreshError as Error,
					attempts,
				});
				console.error('Erro durante o processo de refresh:', refreshError);
				await handleLogout(token);
			}
		} else {
			RefreshTokenManager.resetAttempts();
			AuthEventEmitter.emit('auth:token-expired', { error });
		}
	}

	return Promise.reject(error);
};
