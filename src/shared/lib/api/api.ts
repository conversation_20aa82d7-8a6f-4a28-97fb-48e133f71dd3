import axios from 'axios';
import { cookies } from '../clients/cookies-client';
import { handleAuthError } from '../auth';

export const BaseURL = import.meta.env.VITE_BASE_URL;
const api = axios.create({
	baseURL: BaseURL,
});

api.interceptors.request.use(
	async (config) => {
		const token = await cookies.get('access-token-PSH');
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		return config;
	},
	(error) => {
		return Promise.reject(error instanceof Error ? error : new Error(String(error)));
	},
);

api.interceptors.response.use((response) => response, handleAuthError);
// O interceptor de resposta será configurado após a inicialização para evitar dependência circular
// Veja setupApiInterceptors() em auth/api-interceptors.ts
// setupApiInterceptors();

export default api;
