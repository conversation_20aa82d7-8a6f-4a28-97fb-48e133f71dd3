import { getJWTPayloadProperty } from '../jwt-tools/get-jwt-payload-property.lib';

export interface UserObject {
	name: string;
	socialName: string | null;
	document: string;
	time: string;
	system: number;
	loginId: number;
	roles: number[];
	email: string;
	additionalData: {
		nome: string;
		email: string;
		usuario: string;
		setor: string;
		cargo: string;
		unidade: string;
		empresa: number;
		matricula: number;
	};
	issuedAt: number;
	expiresAt: number;
}

export function buildUserObject(token: string): UserObject {
	const name = getJWTPayloadProperty<string>(token, 'nome') ?? '';
	const socialName = getJWTPayloadProperty<string | null>(token, 'nomeSocial');
	const document = getJWTPayloadProperty<string>(token, 'documento') ?? '';
	const time = getJWTPayloadProperty<string>(token, 'time') ?? '';
	const system = getJWTPayloadProperty<number>(token, 'sistema') ?? 0;
	const loginId = getJWTPayloadProperty<number>(token, 'loginId') ?? 0;
	const roles = getJWTPayloadProperty<number[]>(token, 'roles') ?? [];
	const additionalData = getJWTPayloadProperty<Record<string, unknown>>(token, 'dadosAdicionais') ?? {};
	const issuedAt = getJWTPayloadProperty<number>(token, 'iat') ?? 0;
	const expiresAt = getJWTPayloadProperty<number>(token, 'exp') ?? 0;

	return {
		name,
		socialName,
		document,
		time,
		system,
		loginId,
		roles,
		email: (additionalData.EMAIL as string) ?? '',
		additionalData: {
			nome: (additionalData.NOME as string) ?? '',
			email: (additionalData.EMAIL as string) ?? '',
			usuario: (additionalData.USUARIO as string) ?? '',
			setor: (additionalData.SETOR as string) ?? '',
			cargo: (additionalData.CARGO as string) ?? '',
			unidade: (additionalData.UNIDADE as string) ?? '',
			empresa: (additionalData.EMPRESA as number) ?? 0,
			matricula: (additionalData.MATRICULA as number) ?? 0,
		},
		issuedAt,
		expiresAt,
	};
}
