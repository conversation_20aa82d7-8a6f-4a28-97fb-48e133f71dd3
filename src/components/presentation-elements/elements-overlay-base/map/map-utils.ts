import { IMapMarker, IMapPreview } from '@/pages/edit-apresentation/components/properties-panel/item-editors/map/map-editor.types';

export function getMapStyle(content?: IMapPreview): React.CSSProperties {
	if (!content) return {};
	const { opacity, border, backgroundColor } = content;
	return {
		width: '100%',
		height: '100%',
		opacity,
		border: border ? `${border.width}px ${border.style} ${border.color}` : undefined,
		backgroundColor,
		borderRadius: border?.radius ? `${border.radius}px` : undefined,
	};
}

export interface IMapBounds {
	center: { lat: number; lng: number };
	zoom: number;
}

/**
 * Calcula o centro e zoom ideais para exibir todos os marcadores
 */
export function calculateMapBounds(markers: IMapMarker[]): IMapBounds | null {
	if (markers.length === 0) return null;

	if (markers.length === 1) {
		return {
			center: markers[0].position,
			zoom: 15,
		};
	}

	// Calcular bounds para múltiplos marcadores
	const latitudes = markers.map((marker) => marker.position.lat);
	const longitudes = markers.map((marker) => marker.position.lng);

	const minLat = Math.min(...latitudes);
	const maxLat = Math.max(...latitudes);
	const minLng = Math.min(...longitudes);
	const maxLng = Math.max(...longitudes);

	// Calcular centro
	const centerLat = (minLat + maxLat) / 2;
	const centerLng = (minLng + maxLng) / 2;

	// Calcular zoom baseado na distância
	const latDiff = maxLat - minLat;
	const lngDiff = maxLng - minLng;
	const maxDiff = Math.max(latDiff, lngDiff);

	// Determinar zoom baseado na diferença máxima
	let zoom = 15;
	if (maxDiff > 10) zoom = 5;
	else if (maxDiff > 5) zoom = 6;
	else if (maxDiff > 2) zoom = 7;
	else if (maxDiff > 1) zoom = 8;
	else if (maxDiff > 0.5) zoom = 9;
	else if (maxDiff > 0.25) zoom = 10;
	else if (maxDiff > 0.1) zoom = 11;
	else if (maxDiff > 0.05) zoom = 12;
	else if (maxDiff > 0.01) zoom = 13;
	else if (maxDiff > 0.005) zoom = 14;

	return {
		center: { lat: centerLat, lng: centerLng },
		zoom,
	};
}

/**
 * Verifica se o mapa deve ser auto-ajustado baseado no número de marcadores
 */
export function shouldAutoAdjustMap(markers: IMapMarker[]): boolean {
	return markers.length >= 2;
}
