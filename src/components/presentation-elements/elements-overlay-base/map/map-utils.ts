import { IMapPreview } from '@/pages/edit-apresentation/components/properties-panel/item-editors/map/map-editor.types';

export function getMapStyle(content?: IMapPreview): React.CSSProperties {
	if (!content) return {};
	const { opacity, border, backgroundColor } = content;
	return {
		width: '100%',
		height: '100%',
		opacity,
		border: border ? `${border.width}px ${border.style} ${border.color}` : undefined,
		backgroundColor,
		borderRadius: border?.radius ? `${border.radius}px` : undefined,
	};
}
