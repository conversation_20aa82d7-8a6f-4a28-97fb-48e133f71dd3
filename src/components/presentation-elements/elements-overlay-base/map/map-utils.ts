import { IMapMarker, IMapPreview } from '@/pages/edit-apresentation/components/properties-panel/item-editors/map/map-editor.types';

export function getMapStyle(content?: IMapPreview): React.CSSProperties {
	if (!content) return {};
	const { opacity, border, backgroundColor } = content;
	return {
		width: '100%',
		height: '100%',
		opacity,
		border: border ? `${border.width}px ${border.style} ${border.color}` : undefined,
		backgroundColor,
		borderRadius: border?.radius ? `${border.radius}px` : undefined,
	};
}

export interface IMapBounds {
	center: { lat: number; lng: number };
	zoom: number;
}

/**
 * Calcula o centro e zoom ideais para exibir todos os marcadores
 */
export function calculateMapBounds(markers: IMapMarker[]): IMapBounds | null {
	if (markers.length === 0) return null;

	if (markers.length === 1) {
		return {
			center: markers[0].position,
			zoom: 15,
		};
	}

	// Calcular bounds para múltiplos marcadores
	const latitudes = markers.map((marker) => marker.position.lat);
	const longitudes = markers.map((marker) => marker.position.lng);

	const minLat = Math.min(...latitudes);
	const maxLat = Math.max(...latitudes);
	const minLng = Math.min(...longitudes);
	const maxLng = Math.max(...longitudes);

	// Calcular centro
	const centerLat = (minLat + maxLat) / 2;
	const centerLng = (minLng + maxLng) / 2;

	// Calcular zoom baseado na distância com margem para garantir visibilidade
	const latDiff = maxLat - minLat;
	const lngDiff = maxLng - minLng;
	const maxDiff = Math.max(latDiff, lngDiff);

	// Adicionar margem de 50% para dar mais espaço ao redor dos marcadores
	const margin = 0.5;
	const adjustedDiff = maxDiff * (1 + margin);

	// Determinar zoom baseado na diferença ajustada
	// Garantir zoom mínimo de 8 e máximo de 14 para boa visualização
	let zoom = 12;
	if (adjustedDiff > 10) zoom = Math.max(8, 5);
	else if (adjustedDiff > 5) zoom = Math.max(8, 6);
	else if (adjustedDiff > 2) zoom = Math.max(8, 7);
	else if (adjustedDiff > 1) zoom = Math.max(8, 8);
	else if (adjustedDiff > 0.5) zoom = Math.max(8, 9);
	else if (adjustedDiff > 0.25) zoom = Math.max(8, 10);
	else if (adjustedDiff > 0.1) zoom = Math.max(8, 11);
	else if (adjustedDiff > 0.05) zoom = Math.max(8, 12);
	else if (adjustedDiff > 0.01) zoom = Math.min(14, 13);
	else if (adjustedDiff > 0.005) zoom = Math.min(14, 14);
	else zoom = Math.min(14, 12); // Limitar zoom máximo para não ficar muito próximo

	return {
		center: { lat: centerLat, lng: centerLng },
		zoom,
	};
}

/**
 * Verifica se o mapa deve ser auto-ajustado baseado no número de marcadores
 */
export function shouldAutoAdjustMap(markers: IMapMarker[]): boolean {
	return markers.length >= 2;
}
