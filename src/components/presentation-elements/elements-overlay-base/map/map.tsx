import { IMapPreview } from '@/pages/edit-apresentation/components/properties-panel/item-editors/map/map-editor.types';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { Map, Marker } from '@vis.gl/react-google-maps';
import { useAtomValue } from 'jotai';
import React, { useMemo } from 'react';
import MapPopupOverlay from './map-popup-overlay';
import { mapStyles } from './map-styles';
import { calculateMapBounds, getMapStyle } from './map-utils';

interface MapPreviewProps {
	id: string;
}

export const MapPlaceholder: React.FC<{ message: string }> = ({ message }) => (
	<div className="flex h-full w-full flex-col items-center justify-center overflow-hidden p-2 text-gray-500">
		<div className="flex min-h-[60px] flex-col items-center justify-center text-center">
			<svg width="100%" height="100%" viewBox="0 0 24 24" className="mb-1 h-8 w-8 text-gray-400 sm:h-10 sm:w-10" fill="none">
				<path
					d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
					stroke="currentColor"
					strokeWidth={1.5}
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
			</svg>
			<span className="line-clamp-2 max-w-full break-words text-center text-xs font-medium sm:text-sm">{message}</span>
		</div>
	</div>
);

export const MapPreview: React.FC<MapPreviewProps> = ({ id }) => {
	const items = useAtomValue(itemsAtom);
	const currentItem = items.find((item) => item.id === id);
	const content = currentItem?.content as IMapPreview | undefined;

	// Calcular centro e zoom automaticamente baseado nos marcadores
	const mapConfig = useMemo(() => {
		if (!content) return null;

		const { center, zoom, markers } = content;

		// Se não há marcadores, usar configuração padrão
		if (markers.length === 0) {
			return { center, zoom };
		}

		// Sempre calcular bounds para centralizar todos os marcadores
		const bounds = calculateMapBounds(markers);
		if (bounds) {
			return {
				center: bounds.center,
				zoom: bounds.zoom,
			};
		}

		// Fallback para configuração original
		return { center, zoom };
	}, [content]);

	if (!currentItem || !content) return <MapPlaceholder message="Item não encontrado" />;

	const { markers, gestureHandling, disableDefaultUI, colorScheme } = content;

	if (!window.google?.maps) {
		return <MapPlaceholder message="Google Maps não carregado" />;
	}

	if (!mapConfig) return <MapPlaceholder message="Configuração do mapa inválida" />;

	const mapStylesToUse = colorScheme === 'DARK' ? mapStyles : [];

	return (
		<div style={getMapStyle(content)} className="h-full w-full overflow-hidden">
			<Map
				zoom={mapConfig.zoom}
				center={mapConfig.center}
				gestureHandling={gestureHandling}
				disableDefaultUI={disableDefaultUI}
				colorScheme={colorScheme}
				styles={mapStylesToUse}
				className="h-full w-full"
			>
				{markers.map((marker) => (
					<React.Fragment key={marker.id}>
						<Marker position={marker.position} draggable={marker.draggable} />
						{marker.title && <MapPopupOverlay position={marker.position} content={marker.title} minZoom={12} />}
					</React.Fragment>
				))}
			</Map>
		</div>
	);
};
