export const mapStyles = [
	{ elementType: 'geometry', stylers: [{ color: '#212121' }] },
	{ elementType: 'labels.text.stroke', stylers: [{ color: '#242424' }] },
	{ elementType: 'labels.text.fill', stylers: [{ color: '#a0a0a0' }] },
	{ featureType: 'administrative', elementType: 'geometry.fill', stylers: [{ color: '#1a1a1a' }] },
	{ featureType: 'administrative.locality', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'poi', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'poi.park', elementType: 'geometry', stylers: [{ color: '#1e3229' }] },
	{ featureType: 'poi.business', elementType: 'labels.icon', stylers: [{ saturation: -25 }] },
	{ featureType: 'road', elementType: 'geometry', stylers: [{ color: '#2c2c2c' }] },
	{ featureType: 'road', elementType: 'geometry.stroke', stylers: [{ color: '#242424' }] },
	{ featureType: 'road', elementType: 'labels.text.fill', stylers: [{ color: '#9ca3af' }] },
	{ featureType: 'road.arterial', elementType: 'geometry', stylers: [{ color: '#373737' }] },
	{ featureType: 'road.highway', elementType: 'geometry', stylers: [{ color: '#3c3c3c' }] },
	{ featureType: 'road.highway', elementType: 'geometry.stroke', stylers: [{ color: '#242424' }] },
	{ featureType: 'road.highway', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'transit', elementType: 'geometry', stylers: [{ color: '#2d2d2d' }] },
	{ featureType: 'transit.station', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'water', elementType: 'geometry', stylers: [{ color: '#151a1f' }] },
	{ featureType: 'water', elementType: 'labels.text.fill', stylers: [{ color: '#5e7b91' }] },
];
