import { useMap } from '@vis.gl/react-google-maps';
import React, { useEffect, useRef, useState } from 'react';
import { createRoot } from 'react-dom/client';

interface CustomPopupOverlayProps {
	position: google.maps.LatLngLiteral;
	content: string | React.ReactNode;
	minZoom?: number;
	onClick?: () => void;
	placement?: 'top' | 'bottom' | 'left' | 'right';
}

const MapPopupOverlay: React.FC<CustomPopupOverlayProps> = ({ position, content, minZoom = 10, onClick }) => {
	const mapInstance = useMap();

	const mapOverlayRef = useRef<google.maps.OverlayView | null>(null);
	const [isMapPopupVisible, setMapPopupVisible] = useState(false);

	useEffect(() => {
		if (!mapInstance) return;
		const handleZoomChange = () => {
			const currentMapZoomLevel = mapInstance.getZoom();
			setMapPopupVisible(currentMapZoomLevel !== undefined && currentMapZoomLevel >= minZoom);
		};

		mapInstance.addListener('zoom_changed', handleZoomChange);
		handleZoomChange();

		return () => {
			google.maps.event.clearListeners(mapInstance, 'zoom_changed');
		};
	}, [mapInstance, minZoom, content]);

	useEffect(() => {
		if (!mapInstance || !isMapPopupVisible || typeof mapInstance.getZoom !== 'function') return;

		class LocationTooltip extends google.maps.OverlayView {
			popupPosition: google.maps.LatLng;
			popupContainer: HTMLDivElement;
			reactRoot: any;

			constructor(pos: google.maps.LatLng, innerContent: string | React.ReactNode) {
				super();

				this.popupPosition = pos;

				// Criar o container principal
				this.popupContainer = document.createElement('div');
				this.popupContainer.classList.add(...['absolute', 'w-auto', 'h-auto', 'overflow-visible']);
				// Definir z-index baixo para ficar atrás do marcador
				this.popupContainer.style.zIndex = '1';

				// Criar o container do conteúdo
				const contentContainer = document.createElement('div');
				contentContainer.classList.add(
					...[
						'bg-white',
						'dark:bg-gray-800',
						'rounded-lg',
						'px-3',
						'py-2',
						'text-sm',
						'text-gray-900',
						'dark:text-white',
						'shadow-lg',
						'border',
						'border-gray-200',
						'dark:border-gray-700',
						'max-w-xs',
						'transform',
						'-translate-x-1/2',
						'translate-y-2',
						'relative',
						'cursor-pointer',
					],
				);
				const arrow = document.createElement('div');
				arrow.classList.add(...['absolute', 'bottom-full', 'left-1/2', 'transform', '-translate-x-1/2']);
				arrow.innerHTML = `
          <div class="w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent border-b-white dark:border-b-gray-800"></div>
        `;
				contentContainer.appendChild(arrow);

				if (typeof innerContent === 'string') {
					contentContainer.innerHTML = innerContent + contentContainer.innerHTML;
				} else {
					const contentWrapper = document.createElement('div');
					contentContainer.insertBefore(contentWrapper, contentContainer.firstChild);
					this.reactRoot = createRoot(contentWrapper);
					this.reactRoot.render(innerContent);
				}

				this.popupContainer.appendChild(contentContainer);
				LocationTooltip.preventMapHitsAndGesturesFrom(this.popupContainer);
			}

			onAdd() {
				this.getPanes()?.overlayLayer.appendChild(this.popupContainer);
			}

			onRemove() {
				if (this.reactRoot) {
					this.reactRoot.unmount();
				}
				if (this.popupContainer.parentElement) {
					this.popupContainer.parentElement.removeChild(this.popupContainer);
				}
			}

			draw() {
				const divPosition = this.getProjection().fromLatLngToDivPixel(this.popupPosition);

				if (!divPosition) return;
				const display = Math.abs(divPosition.x) < 4000 && Math.abs(divPosition.y) < 4000 ? 'block' : 'none';
				if (display === 'block') {
					this.popupContainer.style.left = `${divPosition.x}px`;
					this.popupContainer.style.top = `${divPosition.y}px`;
				}
				if (this.popupContainer.style.display !== display) {
					this.popupContainer.style.display = display;
				}
			}
		}

		let coordinates;
		try {
			coordinates = new google.maps.LatLng(position.lat, position.lng);
		} catch (error) {
			console.error('Falha ao criar o objeto LatLng para a posição do pop-up:', error);
			return;
		}

		if (coordinates) {
			const popup = new LocationTooltip(coordinates, content);

			if (mapOverlayRef.current) {
				mapOverlayRef.current.setMap(null);
			}

			mapOverlayRef.current = popup;
			popup.setMap(mapInstance);

			return () => {
				popup.setMap(null);
				mapOverlayRef.current = null;
			};
		}
	}, [mapInstance, position, content, isMapPopupVisible, onClick]);

	return null;
};

export default MapPopupOverlay;
