import { IMapPreview } from '@/pages/edit-apresentation/components/properties-panel/item-editors/map/map-editor.types';
import { itemsAtom } from '@/shared/states/items/object-item.state';
import { Map, Marker } from '@vis.gl/react-google-maps';
import { useAtomValue } from 'jotai';
import React from 'react';
import MapPopupOverlay from './map-popup-overlay';

interface MapPreviewProps {
	id: string;
}

export const MapPlaceholder: React.FC<{ message: string }> = ({ message }) => (
	<div className="flex h-full w-full flex-col items-center justify-center overflow-hidden p-2 text-gray-500">
		<div className="flex min-h-[60px] flex-col items-center justify-center text-center">
			<svg width="100%" height="100%" viewBox="0 0 24 24" className="mb-1 h-8 w-8 text-gray-400 sm:h-10 sm:w-10" fill="none">
				<path
					d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
					stroke="currentColor"
					strokeWidth={1.5}
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
			</svg>
			<span className="line-clamp-2 max-w-full break-words text-center text-xs font-medium sm:text-sm">{message}</span>
		</div>
	</div>
);

export const mapStyles = [
	{ elementType: 'geometry', stylers: [{ color: '#212121' }] },
	{ elementType: 'labels.text.stroke', stylers: [{ color: '#242424' }] },
	{ elementType: 'labels.text.fill', stylers: [{ color: '#a0a0a0' }] },
	{ featureType: 'administrative', elementType: 'geometry.fill', stylers: [{ color: '#1a1a1a' }] },
	{ featureType: 'administrative.locality', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'poi', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'poi.park', elementType: 'geometry', stylers: [{ color: '#1e3229' }] },
	{ featureType: 'poi.business', elementType: 'labels.icon', stylers: [{ saturation: -25 }] },
	{ featureType: 'road', elementType: 'geometry', stylers: [{ color: '#2c2c2c' }] },
	{ featureType: 'road', elementType: 'geometry.stroke', stylers: [{ color: '#242424' }] },
	{ featureType: 'road', elementType: 'labels.text.fill', stylers: [{ color: '#9ca3af' }] },
	{ featureType: 'road.arterial', elementType: 'geometry', stylers: [{ color: '#373737' }] },
	{ featureType: 'road.highway', elementType: 'geometry', stylers: [{ color: '#3c3c3c' }] },
	{ featureType: 'road.highway', elementType: 'geometry.stroke', stylers: [{ color: '#242424' }] },
	{ featureType: 'road.highway', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'transit', elementType: 'geometry', stylers: [{ color: '#2d2d2d' }] },
	{ featureType: 'transit.station', elementType: 'labels.text.fill', stylers: [{ color: '#38d16a' }] },
	{ featureType: 'water', elementType: 'geometry', stylers: [{ color: '#151a1f' }] },
	{ featureType: 'water', elementType: 'labels.text.fill', stylers: [{ color: '#5e7b91' }] },
];

function getMapStyle(content?: IMapPreview): React.CSSProperties {
	if (!content) return {};
	const { opacity, border, backgroundColor } = content;
	return {
		width: '100%',
		height: '100%',
		opacity,
		border: border ? `${border.width}px ${border.style} ${border.color}` : undefined,
		backgroundColor,
		borderRadius: border?.radius ? `${border.radius}px` : undefined,
	};
}

export const MapPreview: React.FC<MapPreviewProps> = ({ id }) => {
	const items = useAtomValue(itemsAtom);
	const currentItem = items.find((item) => item.id === id);
	const content = currentItem?.content as IMapPreview | undefined;

	if (!currentItem || !content) return <MapPlaceholder message="Item não encontrado" />;

	const { center, zoom, markers, gestureHandling, disableDefaultUI, colorScheme } = content;

	if (!window.google?.maps) {
		return <MapPlaceholder message="Google Maps não carregado" />;
	}

	const mapStylesToUse = colorScheme === 'DARK' ? mapStyles : [];

	return (
		<div style={getMapStyle(content)} className="h-full w-full overflow-hidden">
			<Map
				zoom={zoom}
				center={center}
				gestureHandling={gestureHandling}
				disableDefaultUI={disableDefaultUI}
				colorScheme={colorScheme}
				styles={mapStylesToUse}
				className="h-full w-full"
			>
				{markers.map((marker) => (
					<Marker key={marker.id} position={marker.position} draggable={marker.draggable} title={marker.title} />
				))}
			</Map>
		</div>
	);
};
