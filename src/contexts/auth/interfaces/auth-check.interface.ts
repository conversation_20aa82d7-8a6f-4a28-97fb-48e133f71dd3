import { CurrentUser } from '@/shared/interfaces/users/current-user';

export interface IAuthCheckHookReturn {
	isLoading: boolean;
}

export interface IAuthenticateUserService {
	token: string;
	setUser: (user: CurrentUser | null) => void;
	setExpiration: (expiration: Date | undefined) => void;
}

export interface IAuthCheckConfig {
	readonly REFRESH_THRESHOLD_MS: number;
	readonly MAX_REFRESH_ATTEMPTS: number;
}
