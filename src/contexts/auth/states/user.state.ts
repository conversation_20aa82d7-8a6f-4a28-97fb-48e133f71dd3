import { CurrentUser } from '@/shared/interfaces/users/current-user';
import { atom } from 'jotai';

export const userState = atom<CurrentUser | null>(null);

export const getUserInitials = atom((get) => {
	const user = get(userState);
	if (!user) return '';
	const words = user.name.split(' ').filter((word) => word.length > 0);
	if (words.length === 0) return '';
	if (words.length === 1) return words[0].charAt(0).toUpperCase();
	const firstInitial = words[0].charAt(0).toUpperCase();
	const lastInitial = words[words.length - 1].charAt(0).toUpperCase();

	return firstInitial + lastInitial;
});

export const getUserRoles = atom((get) => {
	const user = get(userState);
	if (!user) return [];
	return user.roles || [];
});

export const getUserAdditionalData = atom((get) => {
	const user = get(userState);
	if (!user) return null;
	return user.additionalData;
});

export const getUserDepartment = atom((get) => {
	const user = get(userState);
	if (!user) return '';
	return user.additionalData?.setor || '';
});

export const getUserPosition = atom((get) => {
	const user = get(userState);
	if (!user) return '';
	return user.additionalData?.cargo || '';
});

export const getUserUnit = atom((get) => {
	const user = get(userState);
	if (!user) return '';
	return user.additionalData?.unidade || '';
});

export const getUserCompany = atom((get) => {
	const user = get(userState);
	if (!user) return 0;
	return user.additionalData?.empresa || 0;
});

export const getUserRegistration = atom((get) => {
	const user = get(userState);
	if (!user) return 0;
	return user.additionalData?.matricula || 0;
});

export const getUserDocument = atom((get) => {
	const user = get(userState);
	if (!user) return '';
	return user.document || '';
});

export const getUserSocialName = atom((get) => {
	const user = get(userState);
	if (!user) return null;
	return user.socialName;
});
