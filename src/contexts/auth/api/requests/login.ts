import { ILoginForm } from '@/contexts/auth/validators/login-form';
import api from '@/shared/lib/api/api';
import { resolveGlobalErrors } from '@/shared/lib/errors/handle-global.error';
import { ApiResponse } from '@/shared/types/response';
import { AUTH_ROUTES } from '../endpoints/auth';

export const loginRequest = async ({ user, password }: ILoginForm): Promise<ApiResponse<string>> => {
	try {
		const { data, status } = await api.post(AUTH_ROUTES.LOGIN, {
			user,
			password,
		});
		return { data, status, success: true };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
