import { CurrentUser } from '@/shared/interfaces/users/current-user';
import { getJWTExpiration } from '@/shared/lib/jwt-tools/get-expiration-time';

import { IAuthenticateUserService } from '../interfaces/auth-check.interface';
import { buildUserObject } from '@/shared/lib/api/build-user-obj';

export class AuthCheckService {
	static authenticateUser({ token, setUser, setExpiration }: IAuthenticateUserService): void {
		try {
			if (!token || typeof token !== 'string') throw new Error('Token inválido ou não fornecido');

			const userObject = buildUserObject(token);
			const tokenExpiration = getJWTExpiration(token);
			setUser(userObject);
			setExpiration(tokenExpiration);
		} catch (error) {
			console.error('Erro ao autenticar usuário:', error);
			setUser(null);
			setExpiration(undefined);
			throw error;
		}
	}
	static clearAuthenticationState(setUser: (user: CurrentUser | null) => void, setExpiration: (expiration: Date | undefined) => void): void {
		setUser(null);
		setExpiration(undefined);
	}

	static isValidToken(token: string | undefined): token is string {
		return Boolean(token && typeof token === 'string' && token.trim().length > 0);
	}
}
