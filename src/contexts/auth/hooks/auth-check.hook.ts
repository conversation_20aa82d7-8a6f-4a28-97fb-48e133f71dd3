import { getAuthCookie } from '@/shared/lib/auth/auth-cookies';
import { useAtom, useSetAtom } from 'jotai';

import { useCallback, useEffect, useState } from 'react';
import { IAuthCheckHookReturn } from '../interfaces/auth-check.interface';
import { AuthCheckService } from '../services/auth-check.service';
import { tokenExpirationAtom } from '../states/token-expiration.state';
import { userState } from '../states/user.state';

export const useAuthCheck = (): IAuthCheckHookReturn => {
	const setUser = useSetAtom(userState);
	const [, setExpiration] = useAtom(tokenExpirationAtom);
	const [isLoading, setIsLoading] = useState(true);

	const authenticate = useCallback(async (): Promise<void> => {
		try {
			const token = getAuthCookie();
			if (!AuthCheckService.isValidToken(token)) {
				AuthCheckService.clearAuthenticationState(setUser, setExpiration);
				return;
			}
			AuthCheckService.authenticateUser({
				token,
				setUser,
				setExpiration,
			});
		} catch (error) {
			AuthCheckService.clearAuthenticationState(setUser, setExpiration);
		}
	}, [setUser, setExpiration]);

	useEffect(() => {
		const initializeAuth = async (): Promise<void> => {
			try {
				await authenticate();
			} catch (error) {
				console.error('Erro ao inicializar autenticação:', error);
			} finally {
				setIsLoading(false);
			}
		};

		initializeAuth();
	}, [authenticate]);

	return { isLoading };
};
