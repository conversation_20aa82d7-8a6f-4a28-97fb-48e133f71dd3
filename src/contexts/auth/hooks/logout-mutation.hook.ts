import { getAuthCookie, removeAuth<PERSON>ookie } from '@/shared/lib/auth';
import { toaster } from '@/shared/lib/toaster/toaster';
import { useMutation } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';
import { logoutRequest } from '../api/requests/logout';
import { tokenExpirationAtom } from '../states/token-expiration.state';
import { userState } from '../states/user.state';

export const useLogoutMutation = () => {
	const setUser = useSetAtom(userState);
	const setTokenExpiration = useSetAtom(tokenExpirationAtom);

	const mutation = useMutation({
		mutationKey: ['logout'],
		mutationFn: async () => {
			const token = getAuthCookie();
			if (!token) {
				throw new Error('Token não encontrado');
			}

			const response = await logoutRequest({ token });
			if (!response.success) {
				throw new Error(response.data?.message || 'Erro ao fazer logout');
			}
			return response.data;
		},
		onSuccess: () => {
			removeAuthCookie();
			setUser(null);
			setTokenExpiration(undefined);
			toaster.success('Logout realizado com sucesso');
		},
		onError: (error) => {
			removeAuthCookie();
			setUser(null);
			setTokenExpiration(undefined);

			toaster.error(error.message || 'Erro durante logout');
		},
	});

	return {
		logout: mutation.mutate,
		isLoading: mutation.isPending,
		isError: mutation.isError,
		error: mutation.error,
	};
};
